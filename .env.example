# Firebase Configuration (Required)
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# Firebase Admin SDK (Required for server-side operations)
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_CLIENT_EMAIL=firebase-adminsdk-xxxxx@your_project.iam.gserviceaccount.com
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY\n-----END PRIVATE KEY-----\n"

# EmailJS Configuration (Required for contact forms)
NEXT_PUBLIC_EMAILJS_SERVICE_ID=your_emailjs_service_id
NEXT_PUBLIC_EMAILJS_TEMPLATE_ID_CONTACT=your_contact_template_id
NEXT_PUBLIC_EMAILJS_TEMPLATE_ID_PROJECT=your_project_template_id
NEXT_PUBLIC_EMAILJS_PUBLIC_KEY=your_emailjs_public_key

# Mailchimp Configuration (Required for newsletter)
MAILCHIMP_API_KEY=your_mailchimp_api_key
MAILCHIMP_AUDIENCE_ID=your_audience_id

# Note: The following values are currently hardcoded in the application
# and need to be updated in the source code for your specific setup:
#
# Admin UID: KNlrg408xubJeEmwFpUbeDQWBgF3
# - Update in: src/components/providers/AuthProvider.tsx
# - Update in: src/hooks/useAdminProfile.ts
# - Update in: src/components/comments/ThreadedCommentList.tsx
# - Update in: src/app/dashboard/layout.tsx
# - Update in: src/app/login/page.tsx
# - Update in: src/app/signup/page.tsx
# - Update in: firestore.rules
#
# Contact Email: <EMAIL>
# - Update in: src/lib/email.ts (both contact and project inquiry functions)
