// AI Blog Generator - Main Export File
'use client'

// Core AI functionality
export { aiProviderManager, getRecommendedModel, getCheapestModel, getBestModel } from './providers'
export { optimizedAIManager, getTokenOptimizer, estimateRequestCost, optimizePromptLength } from './optimization'
export { researchEngine, conductKeywordResearch, validateSources } from './research'
export { sitemapAnalyzer, findInternalLinks, insertAutoLinks } from './sitemap'
export { outlineGenerator, generateBlogOutline, validateBlogOutline } from './outline'

// Phase 3: Content Processing and Generation
export { contentGenerator, generateBlogContent, estimateGenerationCost } from './content-generator'
export { markdownFormatter, enhanceContentWithMarkdown, generateMarkdownElement } from './markdown-formatter'
export { internalLinkingEngine, addSmartInternalLinks, getRelatedContentSuggestions, analyzeLinkDensity } from './internal-linking'
export { citationEngine, addExternalCitations, validateCitations, formatBibliography } from './citation-system'
export { metadataGenerator, generateContentMetadata, calculateReadingTime, analyzeContentDifficulty } from './metadata-generator'
export { qualityValidator, validateContentQuality, getQualityScore } from './quality-validator'

// Settings and storage
export {
  saveAISettings,
  getUserAISettings,
  updateAISettings,
  getOrCreateAISettings,
  saveTokenUsage,
  getUserTokenUsage,
  getTotalCostForUser,
  saveCacheEntry,
  getCacheEntry,
  clearExpiredCache,
  validateAISettings,
  createCacheKey
} from './settings'

// Types
export * from '@/types/ai'

// Main AI Blog Generator Class
import { 
  ResearchResult, 
  BlogOutline, 
  GenerationProgress, 
  AISettings,
  AIProvider 
} from '@/types/ai'
import { researchEngine } from './research'
import { outlineGenerator } from './outline'
import { getOrCreateAISettings } from './settings'
import { aiProviderManager } from './providers'

export class AIBlogGenerator {
  private static instance: AIBlogGenerator

  static getInstance(): AIBlogGenerator {
    if (!AIBlogGenerator.instance) {
      AIBlogGenerator.instance = new AIBlogGenerator()
    }
    return AIBlogGenerator.instance
  }

  // Complete blog generation workflow
  async generateBlog(
    keywords: string[],
    userId: string,
    options: {
      targetWordCount?: number
      targetAudience?: string
      contentType?: 'tutorial' | 'guide' | 'analysis' | 'opinion' | 'news'
      maxSources?: number
      includeAcademic?: boolean
      onProgress?: (progress: GenerationProgress) => void
    } = {},
    onProgress?: (progress: GenerationProgress) => void
  ): Promise<{
    research: ResearchResult
    outline: BlogOutline
    settings: AISettings
  }> {
    const progressCallback = options.onProgress || onProgress

    try {
      // Step 1: Get user settings
      progressCallback?.({
        stage: 'research',
        progress: 5,
        message: 'Loading AI settings...',
        tokensUsed: 0,
        estimatedCost: 0
      })

      const settings = await getOrCreateAISettings(userId)

      // Step 2: Conduct research
      progressCallback?.({
        stage: 'research',
        progress: 10,
        message: 'Starting research phase...',
        tokensUsed: 0,
        estimatedCost: 0
      })

      const research = await researchEngine.conductResearch(keywords, userId, {
        maxSources: options.maxSources || 10,
        includeAcademic: options.includeAcademic || false,
        includeNews: true,
        includeBlog: true
      })

      progressCallback?.({
        stage: 'research',
        progress: 50,
        message: `Research completed. Found ${research.sources.length} sources.`,
        tokensUsed: research.tokensUsed,
        estimatedCost: research.cost
      })

      // Step 3: Generate outline
      progressCallback?.({
        stage: 'outline',
        progress: 60,
        message: 'Generating blog outline...',
        tokensUsed: research.tokensUsed,
        estimatedCost: research.cost
      })

      const outline = await outlineGenerator.generateOutline(
        keywords,
        research,
        userId,
        {
          targetWordCount: options.targetWordCount || 2000,
          targetAudience: options.targetAudience || 'intermediate developers',
          contentType: options.contentType || 'guide'
        }
      )

      progressCallback?.({
        stage: 'outline',
        progress: 100,
        message: 'Blog outline generated successfully!',
        tokensUsed: research.tokensUsed,
        estimatedCost: research.cost
      })

      return {
        research,
        outline,
        settings
      }
    } catch (error) {
      console.error('Blog generation failed:', error)
      throw new Error(`Blog generation failed: ${error}`)
    }
  }

  // Get available AI providers
  getAvailableProviders(): AIProvider[] {
    return aiProviderManager.getAvailableProviders()
  }

  // Estimate total cost for blog generation
  async estimateBlogCost(
    keywords: string[],
    userId: string,
    options: any = {}
  ): Promise<number> {
    try {
      const settings = await getOrCreateAISettings(userId)
      
      // Estimate research cost
      const researchCost = aiProviderManager.calculateCost(
        settings.provider,
        settings.models.research,
        1500 // Estimated tokens for research
      )

      // Estimate outline cost
      const outlineCost = aiProviderManager.calculateCost(
        settings.provider,
        settings.models.outline,
        2000 // Estimated tokens for outline
      )

      // Estimate content generation cost (will be implemented in Phase 3)
      const contentCost = aiProviderManager.calculateCost(
        settings.provider,
        settings.models.content,
        (options.targetWordCount || 2000) * 1.5 // Rough token estimation
      )

      return researchCost + outlineCost + contentCost
    } catch (error) {
      console.error('Cost estimation failed:', error)
      return 0
    }
  }

  // Validate blog generation requirements
  validateRequirements(keywords: string[], userId: string): { isValid: boolean; issues: string[] } {
    const issues: string[] = []

    if (!keywords || keywords.length === 0) {
      issues.push('At least one keyword is required')
    }

    if (keywords.some(keyword => keyword.trim().length < 2)) {
      issues.push('Keywords must be at least 2 characters long')
    }

    if (!userId) {
      issues.push('User ID is required')
    }

    const availableProviders = this.getAvailableProviders()
    if (availableProviders.length === 0) {
      issues.push('No AI providers are configured')
    }

    return {
      isValid: issues.length === 0,
      issues
    }
  }
}

// Export singleton instance
export const aiBlogGenerator = AIBlogGenerator.getInstance()

// Utility function for quick blog generation
export const generateAIBlog = async (
  keywords: string[],
  userId: string,
  options?: any,
  onProgress?: (progress: GenerationProgress) => void
) => {
  return await aiBlogGenerator.generateBlog(keywords, userId, options, onProgress)
}

// Check if AI blog generator is ready
export const isAIBlogGeneratorReady = (): boolean => {
  const availableProviders = aiBlogGenerator.getAvailableProviders()
  return availableProviders.length > 0
}

// Get AI provider status
export const getAIProviderStatus = () => {
  return {
    openai: aiProviderManager.isProviderAvailable('openai'),
    gemini: aiProviderManager.isProviderAvailable('gemini'),
    openrouter: aiProviderManager.isProviderAvailable('openrouter')
  }
}
