// AI Error Handling and Retry Logic System
'use client'

export interface RetryOptions {
  maxRetries: number
  baseDelay: number
  maxDelay: number
  backoffMultiplier: number
  retryableErrors: string[]
}

export interface ErrorContext {
  operation: string
  provider: string
  model?: string
  userId: string
  attempt: number
  timestamp: string
}

export class AI<PERSON>rror extends Error {
  public readonly code: string
  public readonly provider: string
  public readonly retryable: boolean
  public readonly context: ErrorContext

  constructor(
    message: string,
    code: string,
    provider: string,
    retryable: boolean = false,
    context: Partial<ErrorContext> = {}
  ) {
    super(message)
    this.name = 'AIError'
    this.code = code
    this.provider = provider
    this.retryable = retryable
    this.context = {
      operation: 'unknown',
      provider,
      userId: 'unknown',
      attempt: 1,
      timestamp: new Date().toISOString(),
      ...context
    }
  }
}

// Error Handler Class
export class AIErrorHandler {
  private static instance: AIErrorHandler
  private errorLog: Map<string, AIError[]> = new Map()

  static getInstance(): AIErrorHandler {
    if (!AIErrorHandler.instance) {
      AIErrorHandler.instance = new AIErrorHandler()
    }
    return AIErrorHandler.instance
  }

  // Main retry wrapper function
  async withRetry<T>(
    operation: () => Promise<T>,
    context: Partial<ErrorContext>,
    options: Partial<RetryOptions> = {}
  ): Promise<T> {
    const retryOptions: RetryOptions = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffMultiplier: 2,
      retryableErrors: [
        'RATE_LIMIT_EXCEEDED',
        'NETWORK_ERROR',
        'TIMEOUT',
        'TEMPORARY_UNAVAILABLE',
        'INTERNAL_ERROR'
      ],
      ...options
    }

    let lastError: Error | null = null
    
    for (let attempt = 1; attempt <= retryOptions.maxRetries + 1; attempt++) {
      try {
        const result = await operation()
        
        // Log successful retry if this wasn't the first attempt
        if (attempt > 1) {
          console.log(`Operation succeeded on attempt ${attempt}`, context)
        }
        
        return result
      } catch (error) {
        lastError = error as Error
        
        // Convert to AIError if not already
        const aiError = this.normalizeError(error as Error, context, attempt)
        
        // Log the error
        this.logError(aiError)
        
        // Check if we should retry
        if (attempt > retryOptions.maxRetries || !this.shouldRetry(aiError, retryOptions)) {
          throw aiError
        }
        
        // Calculate delay for next attempt
        const delay = this.calculateDelay(attempt, retryOptions)
        if (process.env.NODE_ENV === 'development') {
          console.warn(`Attempt ${attempt} failed, retrying in ${delay}ms:`, aiError.message)
        }

        // Wait before retry
        await this.sleep(delay)
      }
    }
    
    throw lastError
  }

  // Normalize different error types to AIError
  private normalizeError(error: Error, context: Partial<ErrorContext>, attempt: number): AIError {
    if (error instanceof AIError) {
      return new AIError(
        error.message,
        error.code,
        error.provider,
        error.retryable,
        { ...error.context, attempt }
      )
    }

    // Detect error types from common AI provider errors
    const errorMessage = error.message.toLowerCase()
    
    if (errorMessage.includes('rate limit') || errorMessage.includes('quota exceeded')) {
      return new AIError(
        error.message,
        'RATE_LIMIT_EXCEEDED',
        context.provider || 'unknown',
        true,
        { ...context, attempt }
      )
    }
    
    if (errorMessage.includes('timeout') || errorMessage.includes('timed out')) {
      return new AIError(
        error.message,
        'TIMEOUT',
        context.provider || 'unknown',
        true,
        { ...context, attempt }
      )
    }
    
    if (errorMessage.includes('network') || errorMessage.includes('connection')) {
      return new AIError(
        error.message,
        'NETWORK_ERROR',
        context.provider || 'unknown',
        true,
        { ...context, attempt }
      )
    }
    
    if (errorMessage.includes('internal server error') || errorMessage.includes('500')) {
      return new AIError(
        error.message,
        'INTERNAL_ERROR',
        context.provider || 'unknown',
        true,
        { ...context, attempt }
      )
    }
    
    if (errorMessage.includes('unauthorized') || errorMessage.includes('invalid api key')) {
      return new AIError(
        error.message,
        'AUTHENTICATION_ERROR',
        context.provider || 'unknown',
        false,
        { ...context, attempt }
      )
    }
    
    if (errorMessage.includes('cost limit') || errorMessage.includes('billing')) {
      return new AIError(
        error.message,
        'COST_LIMIT_EXCEEDED',
        context.provider || 'unknown',
        false,
        { ...context, attempt }
      )
    }
    
    // Default to non-retryable error
    return new AIError(
      error.message,
      'UNKNOWN_ERROR',
      context.provider || 'unknown',
      false,
      { ...context, attempt }
    )
  }

  // Determine if error should be retried
  private shouldRetry(error: AIError, options: RetryOptions): boolean {
    if (!error.retryable) {
      return false
    }
    
    return options.retryableErrors.includes(error.code)
  }

  // Calculate exponential backoff delay
  private calculateDelay(attempt: number, options: RetryOptions): number {
    const delay = options.baseDelay * Math.pow(options.backoffMultiplier, attempt - 1)
    
    // Add jitter to prevent thundering herd
    const jitter = Math.random() * 0.1 * delay
    
    return Math.min(delay + jitter, options.maxDelay)
  }

  // Sleep utility
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // Log errors for monitoring
  private logError(error: AIError): void {
    const userId = error.context.userId
    
    if (!this.errorLog.has(userId)) {
      this.errorLog.set(userId, [])
    }
    
    const userErrors = this.errorLog.get(userId)!
    userErrors.push(error)
    
    // Keep only last 50 errors per user
    if (userErrors.length > 50) {
      userErrors.splice(0, userErrors.length - 50)
    }
    
    // Log errors in development only
    if (process.env.NODE_ENV === 'development') {
      console.error('AI Error:', {
        code: error.code,
        provider: error.provider,
        message: error.message,
        context: error.context,
        retryable: error.retryable
      })
    }
  }

  // Get error statistics for a user
  getErrorStats(userId: string): {
    totalErrors: number
    errorsByCode: Record<string, number>
    errorsByProvider: Record<string, number>
    recentErrors: AIError[]
  } {
    const userErrors = this.errorLog.get(userId) || []
    
    const errorsByCode: Record<string, number> = {}
    const errorsByProvider: Record<string, number> = {}
    
    userErrors.forEach(error => {
      errorsByCode[error.code] = (errorsByCode[error.code] || 0) + 1
      errorsByProvider[error.provider] = (errorsByProvider[error.provider] || 0) + 1
    })
    
    return {
      totalErrors: userErrors.length,
      errorsByCode,
      errorsByProvider,
      recentErrors: userErrors.slice(-10) // Last 10 errors
    }
  }

  // Clear error log for a user
  clearErrorLog(userId: string): void {
    this.errorLog.delete(userId)
  }

  // Provider-specific error handling
  handleProviderError(provider: string, error: Error, context: Partial<ErrorContext>): AIError {
    switch (provider) {
      case 'openai':
        return this.handleOpenAIError(error, context)
      case 'gemini':
        return this.handleGeminiError(error, context)
      case 'openrouter':
        return this.handleOpenRouterError(error, context)
      default:
        return this.normalizeError(error, { ...context, provider }, 1)
    }
  }

  private handleOpenAIError(error: Error, context: Partial<ErrorContext>): AIError {
    const message = error.message.toLowerCase()
    
    if (message.includes('rate_limit_exceeded')) {
      return new AIError(
        'OpenAI rate limit exceeded. Please wait before making more requests.',
        'RATE_LIMIT_EXCEEDED',
        'openai',
        true,
        context
      )
    }
    
    if (message.includes('insufficient_quota')) {
      return new AIError(
        'OpenAI quota exceeded. Please check your billing settings.',
        'QUOTA_EXCEEDED',
        'openai',
        false,
        context
      )
    }
    
    return this.normalizeError(error, { ...context, provider: 'openai' }, 1)
  }

  private handleGeminiError(error: Error, context: Partial<ErrorContext>): AIError {
    const message = error.message.toLowerCase()
    
    if (message.includes('quota exceeded')) {
      return new AIError(
        'Gemini quota exceeded. Please wait or upgrade your plan.',
        'QUOTA_EXCEEDED',
        'gemini',
        true,
        context
      )
    }
    
    return this.normalizeError(error, { ...context, provider: 'gemini' }, 1)
  }

  private handleOpenRouterError(error: Error, context: Partial<ErrorContext>): AIError {
    const message = error.message.toLowerCase()
    
    if (message.includes('insufficient credits')) {
      return new AIError(
        'OpenRouter credits insufficient. Please add credits to your account.',
        'INSUFFICIENT_CREDITS',
        'openrouter',
        false,
        context
      )
    }
    
    return this.normalizeError(error, { ...context, provider: 'openrouter' }, 1)
  }
}

// Export singleton instance
export const aiErrorHandler = AIErrorHandler.getInstance()

// Utility functions
export const withRetry = async <T>(
  operation: () => Promise<T>,
  context: Partial<ErrorContext>,
  options?: Partial<RetryOptions>
): Promise<T> => {
  return await aiErrorHandler.withRetry(operation, context, options)
}

export const handleAIError = (provider: string, error: Error, context: Partial<ErrorContext>): AIError => {
  return aiErrorHandler.handleProviderError(provider, error, context)
}

export const getErrorStats = (userId: string) => {
  return aiErrorHandler.getErrorStats(userId)
}

// Circuit breaker for provider health
export class ProviderCircuitBreaker {
  private failures = new Map<string, number>()
  private lastFailure = new Map<string, number>()
  private readonly failureThreshold = 5
  private readonly recoveryTime = 5 * 60 * 1000 // 5 minutes

  isProviderHealthy(provider: string): boolean {
    const failures = this.failures.get(provider) || 0
    const lastFailure = this.lastFailure.get(provider) || 0
    const now = Date.now()

    // Reset if recovery time has passed
    if (now - lastFailure > this.recoveryTime) {
      this.failures.set(provider, 0)
      return true
    }

    return failures < this.failureThreshold
  }

  recordFailure(provider: string): void {
    const failures = (this.failures.get(provider) || 0) + 1
    this.failures.set(provider, failures)
    this.lastFailure.set(provider, Date.now())
  }

  recordSuccess(provider: string): void {
    this.failures.set(provider, 0)
  }
}

export const providerCircuitBreaker = new ProviderCircuitBreaker()
