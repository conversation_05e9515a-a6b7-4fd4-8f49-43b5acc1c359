// AI Settings API Endpoint
import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { 
  getUserAISettings, 
  saveAISettings, 
  updateAISettings, 
  validateAISettings,
  getAIProviderStatus 
} from '@/lib/ai'
import { verifyIdToken } from '@/lib/firebase-admin'
import { AISettings, DEFAULT_AI_SETTINGS } from '@/types/ai'

export async function GET(request: NextRequest) {
  try {
    // Authentication
    const headersList = headers()
    const authorization = headersList.get('authorization')
    
    if (!authorization?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401 }
      )
    }

    const token = authorization.split('Bearer ')[1]
    const decodedToken = await verifyIdToken(token)
    const userId = decodedToken.uid

    // Get user settings
    const settings = await getUserAISettings(userId)
    
    // Get provider status
    const providerStatus = getAIProviderStatus()

    if (!settings) {
      // Return default settings if none exist
      return NextResponse.json({
        success: true,
        data: {
          ...DEFAULT_AI_SETTINGS,
          user_id: userId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        providerStatus,
        isDefault: true
      })
    }

    return NextResponse.json({
      success: true,
      data: settings,
      providerStatus,
      isDefault: false
    })

  } catch (error) {
    console.error('AI Settings GET Error:', error)
    
    if (error instanceof Error && error.message.includes('Invalid token')) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to retrieve AI settings' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Authentication
    const headersList = headers()
    const authorization = headersList.get('authorization')
    
    if (!authorization?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401 }
      )
    }

    const token = authorization.split('Bearer ')[1]
    const decodedToken = await verifyIdToken(token)
    const userId = decodedToken.uid

    // Parse and validate request body
    const body = await request.json()
    const { settings } = body

    if (!settings || typeof settings !== 'object') {
      return NextResponse.json(
        { error: 'Settings object is required' },
        { status: 400 }
      )
    }

    // Validate settings structure
    const validationErrors = validateAISettings(settings)
    if (validationErrors.length > 0) {
      return NextResponse.json(
        { 
          error: 'Invalid settings',
          details: validationErrors
        },
        { status: 400 }
      )
    }

    // Validate provider availability
    const providerStatus = getAIProviderStatus()
    if (!providerStatus[settings.provider]) {
      return NextResponse.json(
        { 
          error: `Provider '${settings.provider}' is not available`,
          availableProviders: Object.keys(providerStatus).filter(p => providerStatus[p as keyof typeof providerStatus])
        },
        { status: 400 }
      )
    }

    // Save settings
    const settingsId = await saveAISettings(userId, settings)

    // Get the saved settings to return
    const savedSettings = await getUserAISettings(userId)

    return NextResponse.json({
      success: true,
      data: savedSettings,
      message: 'AI settings saved successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('AI Settings POST Error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('Invalid token')) {
        return NextResponse.json(
          { error: 'Invalid authentication token' },
          { status: 401 }
        )
      }
      
      if (error.message.includes('validation')) {
        return NextResponse.json(
          { error: 'Settings validation failed', details: error.message },
          { status: 400 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to save AI settings' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Authentication
    const headersList = headers()
    const authorization = headersList.get('authorization')
    
    if (!authorization?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401 }
      )
    }

    const token = authorization.split('Bearer ')[1]
    const decodedToken = await verifyIdToken(token)
    const userId = decodedToken.uid

    // Parse request body
    const body = await request.json()
    const { updates } = body

    if (!updates || typeof updates !== 'object') {
      return NextResponse.json(
        { error: 'Updates object is required' },
        { status: 400 }
      )
    }

    // Get existing settings
    const existingSettings = await getUserAISettings(userId)
    if (!existingSettings) {
      return NextResponse.json(
        { error: 'No existing settings found. Use POST to create new settings.' },
        { status: 404 }
      )
    }

    // Merge updates with existing settings
    const updatedSettings = {
      ...existingSettings,
      ...updates,
      updated_at: new Date().toISOString()
    }

    // Validate merged settings
    const validationErrors = validateAISettings(updatedSettings)
    if (validationErrors.length > 0) {
      return NextResponse.json(
        { 
          error: 'Invalid settings after update',
          details: validationErrors
        },
        { status: 400 }
      )
    }

    // Update settings
    await updateAISettings(existingSettings.id!, updates)

    // Get updated settings
    const savedSettings = await getUserAISettings(userId)

    return NextResponse.json({
      success: true,
      data: savedSettings,
      message: 'AI settings updated successfully'
    })

  } catch (error) {
    console.error('AI Settings PUT Error:', error)
    
    if (error instanceof Error && error.message.includes('Invalid token')) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to update AI settings' },
      { status: 500 }
    )
  }
}

// DELETE endpoint to reset settings to defaults
export async function DELETE(request: NextRequest) {
  try {
    // Authentication
    const headersList = headers()
    const authorization = headersList.get('authorization')
    
    if (!authorization?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401 }
      )
    }

    const token = authorization.split('Bearer ')[1]
    const decodedToken = await verifyIdToken(token)
    const userId = decodedToken.uid

    // Reset to default settings
    const defaultSettings = {
      ...DEFAULT_AI_SETTINGS,
      user_id: userId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    await saveAISettings(userId, defaultSettings)

    return NextResponse.json({
      success: true,
      data: defaultSettings,
      message: 'AI settings reset to defaults'
    })

  } catch (error) {
    console.error('AI Settings DELETE Error:', error)
    return NextResponse.json(
      { error: 'Failed to reset AI settings' },
      { status: 500 }
    )
  }
}
