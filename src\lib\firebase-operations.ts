'use client'

import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDocs,
  getDoc,
  setDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore'
import { 
  ref, 
  uploadBytes, 
  getDownloadURL, 
  deleteObject 
} from 'firebase/storage'
import { db, storage } from '@/lib/firebase'
import { DatabaseBlogPost, DatabaseProject, UploadedFile, DatabaseComment, CommentWithReplies } from '@/types'

// Generate slug from title
export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9 -]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim()
}

// Calculate reading time
export function calculateReadingTime(content: string): number {
  const wordCount = content.split(/\s+/).length
  return Math.ceil(wordCount / 200)
}

// Blog Post Operations
export async function createBlogPost(
  postData: Omit<DatabaseBlogPost, 'id' | 'created_at' | 'updated_at' | 'slug' | 'author_id' | 'reading_time'>,
  userId: string
): Promise<string> {
  try {
    const slug = generateSlug(postData.title)
    const reading_time = calculateReadingTime(postData.content)
    
    const docRef = await addDoc(collection(db, 'blog_posts'), {
      ...postData,
      slug,
      reading_time,
      author_id: userId,
      created_at: serverTimestamp(),
      updated_at: serverTimestamp(),
    })
    
    return docRef.id
  } catch (error) {
    console.error('Error creating blog post:', error)
    throw error
  }
}

export async function getBlogPostBySlug(slug: string): Promise<DatabaseBlogPost | null> {
  try {
    const q = query(
      collection(db, 'blog_posts'),
      where('slug', '==', slug),
      where('published', '==', true)
    )

    const querySnapshot = await getDocs(q)
    if (querySnapshot.empty) {
      return null
    }

    const doc = querySnapshot.docs[0]
    const data = doc.data() as any
    return {
      id: doc.id,
      ...data,
      created_at: data.created_at?.toDate?.()?.toISOString() || new Date().toISOString(),
      updated_at: data.updated_at?.toDate?.()?.toISOString() || new Date().toISOString(),
    } as DatabaseBlogPost
  } catch (error) {
    console.error('Error getting blog post by slug:', error)
    return null
  }
}

export async function updateBlogPost(
  postId: string,
  postData: Partial<DatabaseBlogPost>
): Promise<void> {
  try {
    const postRef = doc(db, 'blog_posts', postId)
    
    // Update slug if title changed
    if (postData.title) {
      postData.slug = generateSlug(postData.title)
    }
    
    // Update reading time if content changed
    if (postData.content) {
      postData.reading_time = calculateReadingTime(postData.content)
    }
    
    await updateDoc(postRef, {
      ...postData,
      updated_at: serverTimestamp(),
    })
  } catch (error) {
    console.error('Error updating blog post:', error)
    throw error
  }
}

export async function deleteBlogPost(postId: string): Promise<void> {
  try {
    await deleteDoc(doc(db, 'blog_posts', postId))
  } catch (error) {
    console.error('Error deleting blog post:', error)
    throw error
  }
}

export async function getBlogPosts(userId?: string): Promise<DatabaseBlogPost[]> {
  try {
    let q
    if (userId) {
      // Use simple query without orderBy to avoid index requirements
      q = query(
        collection(db, 'blog_posts'),
        where('author_id', '==', userId)
      )
    } else {
      // For all posts, we can use orderBy since there's no where clause
      q = query(
        collection(db, 'blog_posts'),
        orderBy('created_at', 'desc')
      )
    }

    const querySnapshot = await getDocs(q)
    const posts = querySnapshot.docs.map(doc => {
      const data = doc.data() as any
      return {
        id: doc.id,
        ...data,
        created_at: data.created_at?.toDate?.()?.toISOString() || new Date().toISOString(),
        updated_at: data.updated_at?.toDate?.()?.toISOString() || new Date().toISOString(),
      }
    }) as DatabaseBlogPost[]

    // Sort manually if we used a where clause
    if (userId) {
      return posts.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    }

    return posts
  } catch (error) {
    console.error('Error getting blog posts:', error)
    return []
  }
}

export async function getBlogPost(postId: string): Promise<DatabaseBlogPost | null> {
  try {
    const docRef = doc(db, 'blog_posts', postId)
    const docSnap = await getDoc(docRef)
    
    if (docSnap.exists()) {
      const data = docSnap.data() as any
      return {
        id: docSnap.id,
        ...data,
        created_at: data.created_at?.toDate?.()?.toISOString() || new Date().toISOString(),
        updated_at: data.updated_at?.toDate?.()?.toISOString() || new Date().toISOString(),
      } as DatabaseBlogPost
    }
    
    return null
  } catch (error) {
    console.error('Error getting blog post:', error)
    return null
  }
}

// Project Operations
export async function createProject(
  projectData: Omit<DatabaseProject, 'id' | 'created_at' | 'updated_at' | 'slug' | 'author_id'>,
  userId: string
): Promise<string> {
  try {
    const slug = generateSlug(projectData.title)
    
    const docRef = await addDoc(collection(db, 'projects'), {
      ...projectData,
      slug,
      author_id: userId,
      created_at: serverTimestamp(),
      updated_at: serverTimestamp(),
    })
    
    return docRef.id
  } catch (error) {
    console.error('Error creating project:', error)
    throw error
  }
}

export async function updateProject(
  projectId: string,
  projectData: Partial<DatabaseProject>
): Promise<void> {
  try {
    const projectRef = doc(db, 'projects', projectId)
    
    // Update slug if title changed
    if (projectData.title) {
      projectData.slug = generateSlug(projectData.title)
    }
    
    await updateDoc(projectRef, {
      ...projectData,
      updated_at: serverTimestamp(),
    })
  } catch (error) {
    console.error('Error updating project:', error)
    throw error
  }
}

export async function deleteProject(projectId: string): Promise<void> {
  try {
    await deleteDoc(doc(db, 'projects', projectId))
  } catch (error) {
    console.error('Error deleting project:', error)
    throw error
  }
}

export async function getProjects(userId?: string): Promise<DatabaseProject[]> {
  try {
    let q
    if (userId) {
      // Use simple query without orderBy to avoid index requirements
      q = query(
        collection(db, 'projects'),
        where('author_id', '==', userId)
      )
    } else {
      // For all projects, we can use orderBy since there's no where clause
      q = query(
        collection(db, 'projects'),
        orderBy('created_at', 'desc')
      )
    }

    const querySnapshot = await getDocs(q)
    const projects = querySnapshot.docs.map(doc => {
      const data = doc.data() as any
      return {
        id: doc.id,
        ...data,
        created_at: data.created_at?.toDate?.()?.toISOString() || new Date().toISOString(),
        updated_at: data.updated_at?.toDate?.()?.toISOString() || new Date().toISOString(),
      }
    }) as DatabaseProject[]

    // Sort manually if we used a where clause
    if (userId) {
      return projects.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    }

    return projects
  } catch (error) {
    console.error('Error getting projects:', error)
    return []
  }
}

export async function getProject(projectId: string): Promise<DatabaseProject | null> {
  try {
    const docRef = doc(db, 'projects', projectId)
    const docSnap = await getDoc(docRef)
    
    if (docSnap.exists()) {
      const data = docSnap.data() as any
      return {
        id: docSnap.id,
        ...data,
        created_at: data.created_at?.toDate?.()?.toISOString() || new Date().toISOString(),
        updated_at: data.updated_at?.toDate?.()?.toISOString() || new Date().toISOString(),
      } as DatabaseProject
    }
    
    return null
  } catch (error) {
    console.error('Error getting project:', error)
    return null
  }
}

export async function getProjectBySlug(slug: string): Promise<DatabaseProject | null> {
  try {
    const q = query(
      collection(db, 'projects'),
      where('slug', '==', slug),
      where('published', '==', true)
    )

    const querySnapshot = await getDocs(q)
    if (querySnapshot.empty) {
      return null
    }

    const doc = querySnapshot.docs[0]
    const data = doc.data() as any
    return {
      id: doc.id,
      ...data,
      created_at: data.created_at?.toDate?.()?.toISOString() || new Date().toISOString(),
      updated_at: data.updated_at?.toDate?.()?.toISOString() || new Date().toISOString(),
    } as DatabaseProject
  } catch (error) {
    console.error('Error getting project by slug:', error)
    return null
  }
}

// Admin Profile Operations
export async function updateAdminProfile(
  userId: string,
  profileData: {
    displayName?: string
    photoURL?: string
    email?: string
  }
): Promise<void> {
  try {
    const adminProfileRef = doc(db, 'admin_profile', 'profile')
    await setDoc(adminProfileRef, {
      userId,
      ...profileData,
      updated_at: serverTimestamp(),
    }, { merge: true })
  } catch (error) {
    console.error('Error updating admin profile:', error)
    throw error
  }
}

export async function getAdminProfile(): Promise<{
  displayName?: string
  photoURL?: string
  email?: string
  updated_at?: any
} | null> {
  try {
    const adminProfileRef = doc(db, 'admin_profile', 'profile')
    const docSnap = await getDoc(adminProfileRef)

    if (docSnap.exists()) {
      return docSnap.data() as {
        displayName?: string
        photoURL?: string
        email?: string
        updated_at?: any
      }
    }
    return null
  } catch (error) {
    console.error('Error getting admin profile:', error)
    return null
  }
}

// Media Operations
export async function uploadFile(
  file: File,
  userId: string,
  folder: string = 'media'
): Promise<UploadedFile> {
  try {
    const filename = `${Date.now()}-${file.name}`
    const filePath = `${folder}/${userId}/${filename}`
    const storageRef = ref(storage, filePath)

    const snapshot = await uploadBytes(storageRef, file)
    const download_url = await getDownloadURL(snapshot.ref)

    const fileData = {
      filename,
      original_name: file.name,
      file_path: filePath,
      file_size: file.size,
      mime_type: file.type,
      uploaded_by: userId,
      download_url,
      created_at: serverTimestamp(),
    }
    
    const docRef = await addDoc(collection(db, 'uploaded_files'), fileData)
    
    return {
      id: docRef.id,
      ...fileData,
      created_at: new Date().toISOString(),
    } as UploadedFile
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Error uploading file:', error)
    }
    throw error
  }
}

export async function deleteFile(fileId: string, filePath: string): Promise<void> {
  try {
    // Delete from storage
    const storageRef = ref(storage, filePath)
    await deleteObject(storageRef)
    
    // Delete from database
    await deleteDoc(doc(db, 'uploaded_files', fileId))
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Error deleting file:', error)
    }
    throw error
  }
}

export async function getUploadedFiles(userId: string): Promise<UploadedFile[]> {
  try {
    // Use simple query without orderBy to avoid index requirements
    const q = query(
      collection(db, 'uploaded_files'),
      where('uploaded_by', '==', userId)
    )

    const querySnapshot = await getDocs(q)
    const files = querySnapshot.docs.map(doc => {
      const data = doc.data() as any
      return {
        id: doc.id,
        ...data,
        created_at: data.created_at?.toDate?.()?.toISOString() || data.created_at || new Date().toISOString(),
      }
    }) as UploadedFile[]

    // Sort manually by creation date (newest first)
    return files.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
  } catch (error) {
    console.error('Error getting uploaded files:', error)
    return []
  }
}

// Comment Operations
export async function createComment(
  commentData: Omit<DatabaseComment, 'id' | 'created_at' | 'updated_at'>,
): Promise<string> {
  try {
    const docRef = await addDoc(collection(db, 'comments'), {
      ...commentData,
      hearts: [], // Initialize empty hearts array
      created_at: serverTimestamp(),
      updated_at: serverTimestamp(),
    })

    return docRef.id
  } catch (error) {
    console.error('Error creating comment:', error)
    throw error
  }
}

export async function getCommentsByPostId(postId: string, currentUserId?: string): Promise<DatabaseComment[]> {
  try {
    // Simplified query to avoid index issues - we'll filter and sort manually
    const q = query(
      collection(db, 'comments'),
      where('post_id', '==', postId)
    )

    const querySnapshot = await getDocs(q)
    const comments = querySnapshot.docs.map(doc => {
      const data = doc.data() as any
      return {
        id: doc.id,
        ...data,
        created_at: data.created_at?.toDate?.()?.toISOString() || new Date().toISOString(),
        updated_at: data.updated_at?.toDate?.()?.toISOString() || new Date().toISOString(),
        hearts: data.hearts || [], // Ensure hearts array exists
        reply_level: data.reply_level || 0, // Default to 0 for existing comments
        hearted_by_author: data.hearted_by_author || false, // Default to false
        edited: data.edited || false, // Default to false for existing comments
      }
    }) as DatabaseComment[]

    const isAdmin = currentUserId === 'KNlrg408xubJeEmwFpUbeDQWBgF3'

    // Filter comments based on user status
    const visibleComments = comments.filter(comment => {
      // Admin can see all comments
      if (isAdmin) return true

      // Approved comments are visible to everyone
      if (comment.status === 'approved') return true

      // Users can see their own comments (any status)
      if (currentUserId && comment.user_id === currentUserId) return true

      // Otherwise, comment is not visible
      return false
    })

    // Sort by creation date (oldest first for natural conversation flow)
    const sortedComments = visibleComments.sort((a, b) =>
      new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
    )

    return sortedComments
  } catch (error) {
    console.error('Error getting comments:', error)
    return []
  }
}

export async function getAllComments(userId?: string): Promise<DatabaseComment[]> {
  try {
    // Simplified query to avoid index issues
    const q = query(collection(db, 'comments'))

    const querySnapshot = await getDocs(q)
    const comments = querySnapshot.docs.map(doc => {
      const data = doc.data() as any
      return {
        id: doc.id,
        ...data,
        created_at: data.created_at?.toDate?.()?.toISOString() || new Date().toISOString(),
        updated_at: data.updated_at?.toDate?.()?.toISOString() || new Date().toISOString(),
        hearts: data.hearts || [], // Ensure hearts array exists
        reply_level: data.reply_level || 0, // Default to 0 for existing comments
        hearted_by_author: data.hearted_by_author || false, // Default to false
        edited: data.edited || false, // Default to false for existing comments
      }
    }) as DatabaseComment[]

    // Sort manually by creation date (newest first)
    const sortedComments = comments.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

    // If no userId provided, filter for approved comments only (public view)
    if (!userId) {
      return sortedComments.filter(comment => comment.status === 'approved')
    }

    // Admin can see all comments
    return sortedComments
  } catch (error) {
    console.error('Error getting all comments:', error)
    return []
  }
}

export async function updateCommentStatus(
  commentId: string,
  status: 'pending' | 'approved' | 'spam' | 'deleted'
): Promise<void> {
  try {
    const commentRef = doc(db, 'comments', commentId)
    await updateDoc(commentRef, {
      status,
      updated_at: serverTimestamp(),
    })
  } catch (error) {
    console.error('Error updating comment status:', error)
    throw error
  }
}

export async function updateComment(
  commentId: string,
  content: string
): Promise<void> {
  try {
    const commentRef = doc(db, 'comments', commentId)
    await updateDoc(commentRef, {
      content: content.trim(),
      updated_at: serverTimestamp(),
      edited: true, // Mark comment as edited
    })
  } catch (error) {
    console.error('Error updating comment:', error)
    throw error
  }
}

export async function deleteComment(commentId: string): Promise<void> {
  try {
    await deleteDoc(doc(db, 'comments', commentId))
  } catch (error) {
    console.error('Error deleting comment:', error)
    throw error
  }
}

// Heart reaction functions
export async function toggleCommentHeart(commentId: string, userId: string, isAuthor: boolean = false): Promise<void> {
  try {
    const commentRef = doc(db, 'comments', commentId)
    const commentDoc = await getDoc(commentRef)

    if (!commentDoc.exists()) {
      throw new Error('Comment not found')
    }

    const commentData = commentDoc.data()
    const currentHearts = commentData.hearts || []
    const hasHearted = currentHearts.includes(userId)

    let newHearts: string[]
    if (hasHearted) {
      // Remove heart
      newHearts = currentHearts.filter((id: string) => id !== userId)
    } else {
      // Add heart
      newHearts = [...currentHearts, userId]
    }

    const updateData: any = {
      hearts: newHearts,
      updated_at: serverTimestamp(),
    }

    // If author is hearting/unhearting, update the special flag
    if (isAuthor) {
      updateData.hearted_by_author = !hasHearted
    }

    await updateDoc(commentRef, updateData)
  } catch (error) {
    console.error('Error toggling comment heart:', error)
    throw error
  }
}

// Analytics and engagement functions
export async function getEngagementAnalytics(): Promise<{
  totalComments: number;
  totalHearts: number;
  totalViews: number;
  commentsByPost: { [postId: string]: number };
  heartsByPost: { [postId: string]: number };
  viewsBySlug: { [slug: string]: number };
  recentActivity: Array<{
    type: 'comment' | 'heart' | 'view';
    postId?: string;
    slug?: string;
    timestamp: string;
    count?: number;
  }>;
}> {
  try {
    // Get all comments
    const commentsQuery = query(collection(db, 'comments'))
    const commentsSnapshot = await getDocs(commentsQuery)

    let totalComments = 0
    let totalHearts = 0
    const commentsByPost: { [postId: string]: number } = {}
    const heartsByPost: { [postId: string]: number } = {}

    commentsSnapshot.docs.forEach(doc => {
      const comment = doc.data() as DatabaseComment
      totalComments++

      // Count comments by post
      commentsByPost[comment.post_id] = (commentsByPost[comment.post_id] || 0) + 1

      // Count hearts
      const hearts = comment.hearts?.length || 0
      totalHearts += hearts
      heartsByPost[comment.post_id] = (heartsByPost[comment.post_id] || 0) + hearts
    })

    // Get views from localStorage (this would be server-side in production)
    const viewsBySlug: { [slug: string]: number } = {}
    let totalViews = 0

    if (typeof window !== 'undefined') {
      try {
        const storedViews = localStorage.getItem('blog-views')
        if (storedViews) {
          const views: Record<string, number> = JSON.parse(storedViews)
          Object.assign(viewsBySlug, views)
          totalViews = Object.values(views).reduce((sum, count) => sum + count, 0)
        }
      } catch (error) {
        console.error('Error reading views:', error)
      }
    }

    // Create recent activity (simplified for demo)
    const recentActivity = [
      { type: 'comment' as const, timestamp: new Date().toISOString(), count: totalComments },
      { type: 'heart' as const, timestamp: new Date().toISOString(), count: totalHearts },
      { type: 'view' as const, timestamp: new Date().toISOString(), count: totalViews },
    ]

    return {
      totalComments,
      totalHearts,
      totalViews,
      commentsByPost,
      heartsByPost,
      viewsBySlug,
      recentActivity
    }
  } catch (error) {
    console.error('Error getting engagement analytics:', error)
    return {
      totalComments: 0,
      totalHearts: 0,
      totalViews: 0,
      commentsByPost: {},
      heartsByPost: {},
      viewsBySlug: {},
      recentActivity: []
    }
  }
}

// Function to organize comments into threaded structure
export function organizeCommentsIntoThreads(comments: DatabaseComment[]): CommentWithReplies[] {
  const commentMap = new Map<string, CommentWithReplies>()
  const rootComments: CommentWithReplies[] = []

  // First pass: create all comment objects with empty replies
  comments.forEach(comment => {
    commentMap.set(comment.id, { ...comment, replies: [] })
  })

  // Second pass: organize into hierarchy
  comments.forEach(comment => {
    const commentWithReplies = commentMap.get(comment.id)!

    if (!comment.parent_id) {
      // Root level comment
      rootComments.push(commentWithReplies)
    } else {
      // Reply to another comment
      const parentComment = commentMap.get(comment.parent_id)
      if (parentComment) {
        parentComment.replies.push(commentWithReplies)
      }
    }
  })

  // Sort root comments by creation date (oldest first)
  rootComments.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())

  // Sort replies within each thread
  const sortReplies = (comment: CommentWithReplies) => {
    comment.replies.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
    comment.replies.forEach(sortReplies)
  }

  rootComments.forEach(sortReplies)

  return rootComments
}
