'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import {
  SparklesIcon,
  CogIcon,
  DocumentTextIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  RocketLaunchIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import {
  DashboardCard,
  DashboardCardContent,
  DashboardCardDescription,
  DashboardCardHeader,
  DashboardCardTitle
} from '@/components/dashboard/DashboardCard'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import Link from 'next/link'
import { getAIProviderStatus, getTotalCostForUser, getUserAISettings } from '@/lib/ai'
import { AISettings } from '@/types/ai'

export default function AIGeneratorPage() {
  const { user } = useAuth()
  const [aiSettings, setAiSettings] = useState<AISettings | null>(null)
  const [providerStatus, setProviderStatus] = useState({
    openai: false,
    gemini: false,
    openrouter: false
  })
  const [monthlyUsage, setMonthlyUsage] = useState(0)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user) {
      loadAIData()
    }
  }, [user])

  const loadAIData = async () => {
    if (!user) return

    try {
      const [settings, status, usage] = await Promise.all([
        getUserAISettings(user.uid),
        Promise.resolve(getAIProviderStatus()),
        getTotalCostForUser(user.uid, 30)
      ])

      setAiSettings(settings)
      setProviderStatus(status)
      setMonthlyUsage(usage)
    } catch (error) {
      console.error('Error loading AI data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getProviderBadgeColor = (isAvailable: boolean) => {
    return isAvailable ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
  }

  const workflowSteps = [
    {
      step: 1,
      title: 'Keywords & Research',
      description: 'Enter keywords and let AI conduct deep research',
      icon: MagnifyingGlassIcon,
      href: '/dashboard/ai-generator/research',
      color: 'bg-blue-500'
    },
    {
      step: 2,
      title: 'Generate Outline',
      description: 'AI creates a comprehensive blog outline',
      icon: DocumentTextIcon,
      href: '/dashboard/ai-generator/outline',
      color: 'bg-purple-500'
    },
    {
      step: 3,
      title: 'Content Generation',
      description: 'Generate content in batches with rich formatting',
      icon: PencilIcon,
      href: '/dashboard/ai-generator/generate',
      color: 'bg-green-500'
    },
    {
      step: 4,
      title: 'Review & Publish',
      description: 'Review, edit, and publish your AI-generated blog',
      icon: RocketLaunchIcon,
      href: '/dashboard/ai-generator/review',
      color: 'bg-orange-500'
    }
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading AI Generator...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight dashboard-text flex items-center gap-3">
            <SparklesIcon className="w-8 h-8 text-blue-600" />
            AI Blog Generator
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2 text-lg">
            Generate high-quality blog posts with AI-powered research and content creation
          </p>
        </div>
        <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700 text-white">
          <Link href="/dashboard/ai-generator/settings">
            <CogIcon className="w-5 h-5 mr-2" />
            AI Settings
          </Link>
        </Button>
      </div>

      {/* Main Action - Start Generation */}
      <DashboardCard className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-200 dark:border-blue-800">
        <DashboardCardContent className="p-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <SparklesIcon className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold dashboard-text mb-2">Ready to Create?</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
              Start your AI-powered blog generation journey. Research keywords, create outlines, and generate complete blog posts.
            </p>
            <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8">
              <Link href="/dashboard/ai-generator/research">
                <RocketLaunchIcon className="w-5 h-5 mr-2" />
                Start New Blog Generation
              </Link>
            </Button>
          </div>
        </DashboardCardContent>
      </DashboardCard>

      {/* Status and Settings */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* AI Providers */}
        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle>AI Providers</DashboardCardTitle>
            <DashboardCardDescription>
              Available AI providers for content generation
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent>
            <div className="space-y-3">
              {Object.entries(providerStatus).map(([provider, available]) => (
                <div key={provider} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${available ? 'bg-green-500' : 'bg-gray-400'}`} />
                    <span className="text-sm font-medium capitalize">{provider}</span>
                  </div>
                  <Badge variant={available ? "default" : "outline"}>
                    {available ? "Available" : "Not Available"}
                  </Badge>
                </div>
              ))}
            </div>
          </DashboardCardContent>
        </DashboardCard>

        {/* Current Settings */}
        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle>Current Settings</DashboardCardTitle>
            <DashboardCardDescription>
              Your AI generation preferences
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent>
            {aiSettings ? (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Provider</span>
                  <Badge variant="outline" className="capitalize">
                    {aiSettings.provider}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Content Model</span>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {aiSettings.models.content}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Temperature</span>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {aiSettings.preferences.temperature}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Caching</span>
                  <Badge variant={aiSettings.preferences.enableCaching ? "default" : "outline"}>
                    {aiSettings.preferences.enableCaching ? "Enabled" : "Disabled"}
                  </Badge>
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Loading settings...
                </p>
              </div>
            )}
          </DashboardCardContent>
        </DashboardCard>

        {/* Usage Stats */}
        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle>Monthly Usage</DashboardCardTitle>
            <DashboardCardDescription>
              AI generation costs this month
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent>
            <div className="text-center py-4">
              <div className="text-2xl font-bold dashboard-text">
                ${monthlyUsage.toFixed(2)}
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {aiSettings ? `of $${aiSettings.preferences.costLimit} limit` : 'No limit set'}
              </p>
              {aiSettings && (
                <div className="mt-3">
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{
                        width: `${Math.min((monthlyUsage / aiSettings.preferences.costLimit) * 100, 100)}%`
                      }}
                    />
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {((monthlyUsage / aiSettings.preferences.costLimit) * 100).toFixed(1)}% of limit used
                  </p>
                </div>
              )}
            </div>
          </DashboardCardContent>
        </DashboardCard>
      </div>

      {/* AI Generation Workflow */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold dashboard-text">AI Generation Workflow</h3>
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
            <span>Progress:</span>
            <div className="w-32 h-2 bg-gray-200 dark:bg-gray-700 rounded-full">
              <div className="w-1/4 h-2 bg-blue-600 rounded-full"></div>
            </div>
            <span>25%</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Step 1: Research */}
          <Link href="/dashboard/ai-generator/research">
            <DashboardCard className="relative hover:shadow-lg transition-all duration-300 cursor-pointer group hover:scale-105 border-2 border-blue-200 dark:border-blue-800">
              <DashboardCardHeader>
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center group-hover:bg-blue-600 transition-colors">
                    <span className="text-lg font-bold text-blue-600 dark:text-blue-400 group-hover:text-white">1</span>
                  </div>
                  <DashboardCardTitle className="text-lg">Research</DashboardCardTitle>
                </div>
                <DashboardCardDescription>
                  AI researches your topic and finds relevant keywords
                </DashboardCardDescription>
              </DashboardCardHeader>
              <DashboardCardContent>
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-4">
                  <MagnifyingGlassIcon className="w-4 h-4" />
                  <span>Keyword research & analysis</span>
                </div>
                <div className="flex items-center justify-between">
                  <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                    Ready
                  </Badge>
                  <span className="text-xs text-gray-500">~2 min</span>
                </div>
              </DashboardCardContent>
            </DashboardCard>
          </Link>

          {/* Step 2: Outline */}
          <DashboardCard className="relative hover:shadow-lg transition-all duration-300 opacity-60">
            <DashboardCardHeader>
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                  <span className="text-lg font-bold text-gray-400">2</span>
                </div>
                <DashboardCardTitle className="text-lg">Outline</DashboardCardTitle>
              </div>
              <DashboardCardDescription>
                AI creates a comprehensive blog structure
              </DashboardCardDescription>
            </DashboardCardHeader>
            <DashboardCardContent>
              <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-4">
                <DocumentTextIcon className="w-4 h-4" />
                <span>Structure & organization</span>
              </div>
              <div className="flex items-center justify-between">
                <Badge variant="outline">
                  Pending
                </Badge>
                <span className="text-xs text-gray-500">~3 min</span>
              </div>
            </DashboardCardContent>
          </DashboardCard>

          {/* Step 3: Generate */}
          <DashboardCard className="relative hover:shadow-lg transition-all duration-300 opacity-60">
            <DashboardCardHeader>
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                  <span className="text-lg font-bold text-gray-400">3</span>
                </div>
                <DashboardCardTitle className="text-lg">Generate</DashboardCardTitle>
              </div>
              <DashboardCardDescription>
                AI writes your complete blog post content
              </DashboardCardDescription>
            </DashboardCardHeader>
            <DashboardCardContent>
              <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-4">
                <PencilIcon className="w-4 h-4" />
                <span>Full content generation</span>
              </div>
              <div className="flex items-center justify-between">
                <Badge variant="outline">
                  Pending
                </Badge>
                <span className="text-xs text-gray-500">~5 min</span>
              </div>
            </DashboardCardContent>
          </DashboardCard>

          {/* Step 4: Publish */}
          <DashboardCard className="relative hover:shadow-lg transition-all duration-300 opacity-60">
            <DashboardCardHeader>
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                  <span className="text-lg font-bold text-gray-400">4</span>
                </div>
                <DashboardCardTitle className="text-lg">Publish</DashboardCardTitle>
              </div>
              <DashboardCardDescription>
                Review, edit and publish your blog post
              </DashboardCardDescription>
            </DashboardCardHeader>
            <DashboardCardContent>
              <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-4">
                <RocketLaunchIcon className="w-4 h-4" />
                <span>Final review & publishing</span>
              </div>
              <div className="flex items-center justify-between">
                <Badge variant="outline">
                  Pending
                </Badge>
                <span className="text-xs text-gray-500">~2 min</span>
              </div>
            </DashboardCardContent>
          </DashboardCard>
        </div>

        {/* Workflow Tips */}
        <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-start gap-3">
            <InformationCircleIcon className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-semibold text-blue-800 dark:text-blue-200">Workflow Tips</h4>
              <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                Follow the steps in order for best results. Each step builds on the previous one to create high-quality, SEO-optimized blog content.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
