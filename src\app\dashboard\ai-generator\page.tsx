'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { 
  SparklesIcon, 
  CogIcon, 
  DocumentTextIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  RocketLaunchIcon
} from '@heroicons/react/24/outline'
import {
  DashboardCard,
  DashboardCardContent,
  DashboardCardDescription,
  DashboardCardHeader,
  DashboardCardTitle
} from '@/components/dashboard/DashboardCard'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import Link from 'next/link'
import { getAIProviderStatus, getTotalCostForUser, getUserAISettings } from '@/lib/ai'
import { AISettings } from '@/types/ai'

export default function AIGeneratorPage() {
  const { user } = useAuth()
  const [aiSettings, setAiSettings] = useState<AISettings | null>(null)
  const [providerStatus, setProviderStatus] = useState({
    openai: false,
    gemini: false,
    openrouter: false
  })
  const [monthlyUsage, setMonthlyUsage] = useState(0)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user) {
      loadAIData()
    }
  }, [user])

  const loadAIData = async () => {
    if (!user) return

    try {
      const [settings, status, usage] = await Promise.all([
        getUserAISettings(user.uid),
        Promise.resolve(getAIProviderStatus()),
        getTotalCostForUser(user.uid, 30)
      ])

      setAiSettings(settings)
      setProviderStatus(status)
      setMonthlyUsage(usage)
    } catch (error) {
      console.error('Error loading AI data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getProviderBadgeColor = (isAvailable: boolean) => {
    return isAvailable ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
  }

  const workflowSteps = [
    {
      step: 1,
      title: 'Keywords & Research',
      description: 'Enter keywords and let AI conduct deep research',
      icon: MagnifyingGlassIcon,
      href: '/dashboard/ai-generator/research',
      color: 'bg-blue-500'
    },
    {
      step: 2,
      title: 'Generate Outline',
      description: 'AI creates a comprehensive blog outline',
      icon: DocumentTextIcon,
      href: '/dashboard/ai-generator/outline',
      color: 'bg-purple-500'
    },
    {
      step: 3,
      title: 'Content Generation',
      description: 'Generate content in batches with rich formatting',
      icon: PencilIcon,
      href: '/dashboard/ai-generator/generate',
      color: 'bg-green-500'
    },
    {
      step: 4,
      title: 'Review & Publish',
      description: 'Review, edit, and publish your AI-generated blog',
      icon: RocketLaunchIcon,
      href: '/dashboard/ai-generator/review',
      color: 'bg-orange-500'
    }
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading AI Generator...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight dashboard-text flex items-center gap-2">
            <SparklesIcon className="w-7 h-7 text-blue-600" />
            AI Blog Generator
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Generate high-quality blog posts with AI-powered research and content creation
          </p>
        </div>
        <Button asChild>
          <Link href="/dashboard/ai-generator/settings">
            <CogIcon className="w-4 h-4 mr-2" />
            AI Settings
          </Link>
        </Button>
      </div>

      {/* Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* AI Provider Status */}
        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle>AI Providers</DashboardCardTitle>
            <DashboardCardDescription>
              Available AI providers for content generation
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">OpenAI</span>
                <Badge className={getProviderBadgeColor(providerStatus.openai)}>
                  {providerStatus.openai ? 'Available' : 'Not Configured'}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Gemini</span>
                <Badge className={getProviderBadgeColor(providerStatus.gemini)}>
                  {providerStatus.gemini ? 'Available' : 'Not Configured'}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">OpenRouter</span>
                <Badge className={getProviderBadgeColor(providerStatus.openrouter)}>
                  {providerStatus.openrouter ? 'Available' : 'Not Configured'}
                </Badge>
              </div>
            </div>
          </DashboardCardContent>
        </DashboardCard>

        {/* Current Settings */}
        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle>Current Settings</DashboardCardTitle>
            <DashboardCardDescription>
              Your AI generation preferences
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent>
            {aiSettings ? (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Provider</span>
                  <Badge variant="outline" className="capitalize">
                    {aiSettings.provider}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Content Model</span>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {aiSettings.models.content}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Cost Limit</span>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    ${aiSettings.preferences.costLimit}
                  </span>
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  No settings configured yet
                </p>
                <Button asChild size="sm">
                  <Link href="/dashboard/ai-generator/settings">
                    Configure Settings
                  </Link>
                </Button>
              </div>
            )}
          </DashboardCardContent>
        </DashboardCard>

        {/* Usage Stats */}
        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle>Monthly Usage</DashboardCardTitle>
            <DashboardCardDescription>
              AI generation costs this month
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent>
            <div className="text-center py-4">
              <div className="text-2xl font-bold dashboard-text">
                ${monthlyUsage.toFixed(2)}
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {aiSettings ? `of $${aiSettings.preferences.costLimit} limit` : 'No limit set'}
              </p>
              {aiSettings && (
                <div className="mt-3">
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ 
                        width: `${Math.min((monthlyUsage / aiSettings.preferences.costLimit) * 100, 100)}%` 
                      }}
                    />
                  </div>
                </div>
              )}
            </div>
          </DashboardCardContent>
        </DashboardCard>
      </div>

      {/* Workflow Steps */}
      <DashboardCard>
        <DashboardCardHeader>
          <DashboardCardTitle>AI Blog Generation Workflow</DashboardCardTitle>
          <DashboardCardDescription>
            Follow these steps to create AI-powered blog content
          </DashboardCardDescription>
        </DashboardCardHeader>
        <DashboardCardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {workflowSteps.map((step) => (
              <div key={step.step} className="relative">
                <Button
                  asChild
                  variant="outline"
                  className="h-auto p-6 flex flex-col items-center text-center space-y-4 w-full hover:shadow-md transition-all duration-200"
                >
                  <Link href={step.href}>
                    <div className={`w-12 h-12 rounded-full ${step.color} flex items-center justify-center text-white mb-2`}>
                      <step.icon className="w-6 h-6" />
                    </div>
                    <div>
                      <div className="font-semibold text-sm mb-1">
                        Step {step.step}: {step.title}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">
                        {step.description}
                      </div>
                    </div>
                  </Link>
                </Button>
                {step.step < workflowSteps.length && (
                  <div className="hidden lg:block absolute top-1/2 -right-3 transform -translate-y-1/2">
                    <div className="w-6 h-0.5 bg-gray-300 dark:bg-gray-600"></div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </DashboardCardContent>
      </DashboardCard>

      {/* Quick Start */}
      <DashboardCard>
        <DashboardCardHeader>
          <DashboardCardTitle>Quick Start</DashboardCardTitle>
          <DashboardCardDescription>
            Get started with AI blog generation
          </DashboardCardDescription>
        </DashboardCardHeader>
        <DashboardCardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button asChild className="flex-1">
              <Link href="/dashboard/ai-generator/research">
                <SparklesIcon className="w-4 h-4 mr-2" />
                Start New Blog Generation
              </Link>
            </Button>
            <Button asChild variant="outline" className="flex-1">
              <Link href="/dashboard/ai-generator/settings">
                <CogIcon className="w-4 h-4 mr-2" />
                Configure AI Settings
              </Link>
            </Button>
          </div>
        </DashboardCardContent>
      </DashboardCard>
    </div>
  )
}
