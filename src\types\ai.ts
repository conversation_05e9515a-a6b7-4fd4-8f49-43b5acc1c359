// AI Provider Types and Interfaces

export type AIProvider = 'openai' | 'openrouter' | 'gemini'

export type TaskType = 'research' | 'outline' | 'content' | 'metadata'

export interface AIModel {
  id: string
  name: string
  provider: AIProvider
  costPer1kTokens: number
  maxTokens: number
  description: string
  recommended: TaskType[]
}

export interface AIProviderConfig {
  provider: AIProvider
  apiKey: string
  baseUrl?: string
  models: AIModel[]
}

export interface AISettings {
  id?: string
  user_id: string
  provider: AIProvider
  models: {
    research: string
    outline: string
    content: string
    metadata: string
  }
  preferences: {
    maxTokensPerRequest: number
    temperature: number
    enableCaching: boolean
    costLimit: number // in USD
  }
  created_at: string
  updated_at: string
}

export interface AIRequest {
  provider: AIProvider
  model: string
  prompt: string
  maxTokens?: number
  temperature?: number
  systemPrompt?: string
}

export interface AIResponse {
  content: string
  tokensUsed: number
  cost: number
  provider: AIProvider
  model: string
  cached: boolean
}

export interface ResearchResult {
  query: string
  sources: ResearchSource[]
  summary: string
  keyPoints: string[]
  relatedTopics: string[]
  tokensUsed: number
  cost: number
}

export interface ResearchSource {
  url: string
  title: string
  content: string
  relevanceScore: number
  datePublished?: string
  author?: string
  domain: string
}

export interface BlogOutline {
  title: string
  introduction: OutlineSection
  chapters: OutlineChapter[]
  conclusion: OutlineSection
  metadata: {
    estimatedWordCount: number
    targetAudience: string
    seoKeywords: string[]
    categories: string[]
    tags: string[]
  }
  internalLinks: InternalLink[]
  externalSources: ResearchSource[]
}

export interface OutlineSection {
  title: string
  content: string
  keyPoints: string[]
  wordCount: number
}

export interface OutlineChapter {
  title: string
  sections: OutlineSection[]
  includeTable: boolean
  includeQuote: boolean
  includeCodeBlock: boolean
  estimatedWordCount: number
}

export interface InternalLink {
  text: string
  url: string
  relevanceScore: number
  context: string
}

export interface GenerationProgress {
  stage: 'research' | 'outline' | 'content' | 'complete'
  currentChapter?: number
  totalChapters?: number
  progress: number // 0-100
  message: string
  tokensUsed: number
  estimatedCost: number
}

export interface TokenUsage {
  provider: AIProvider
  model: string
  promptTokens: number
  completionTokens: number
  totalTokens: number
  cost: number
  timestamp: string
}

export interface CacheEntry {
  key: string
  content: string
  provider: AIProvider
  model: string
  expiresAt: string
  tokensUsed: number
}

// Available AI Models Configuration
export const AI_MODELS: Record<AIProvider, AIModel[]> = {
  openai: [
    {
      id: 'gpt-3.5-turbo',
      name: 'GPT-3.5 Turbo',
      provider: 'openai',
      costPer1kTokens: 0.002,
      maxTokens: 4096,
      description: 'Fast and cost-effective for basic tasks',
      recommended: ['research', 'metadata']
    },
    {
      id: 'gpt-4o-mini',
      name: 'GPT-4o Mini',
      provider: 'openai',
      costPer1kTokens: 0.15,
      maxTokens: 128000,
      description: 'Balanced performance and cost',
      recommended: ['outline', 'content']
    },
    {
      id: 'gpt-4o',
      name: 'GPT-4o',
      provider: 'openai',
      costPer1kTokens: 5.0,
      maxTokens: 128000,
      description: 'Highest quality for complex content',
      recommended: ['content']
    }
  ],
  gemini: [
    {
      id: 'gemini-1.5-flash',
      name: 'Gemini 1.5 Flash',
      provider: 'gemini',
      costPer1kTokens: 0.00015,
      maxTokens: 1000000,
      description: 'Ultra-fast and cost-effective',
      recommended: ['research', 'metadata']
    },
    {
      id: 'gemini-1.5-pro',
      name: 'Gemini 1.5 Pro',
      provider: 'gemini',
      costPer1kTokens: 0.00125,
      maxTokens: 2000000,
      description: 'High-quality reasoning and generation',
      recommended: ['outline', 'content']
    }
  ],
  openrouter: [
    {
      id: 'meta-llama/llama-3.1-8b-instruct',
      name: 'Llama 3.1 8B',
      provider: 'openrouter',
      costPer1kTokens: 0.0002,
      maxTokens: 128000,
      description: 'Open source, very cost-effective',
      recommended: ['research', 'metadata']
    },
    {
      id: 'anthropic/claude-3.5-sonnet',
      name: 'Claude 3.5 Sonnet',
      provider: 'openrouter',
      costPer1kTokens: 3.0,
      maxTokens: 200000,
      description: 'Excellent for long-form content',
      recommended: ['content']
    },
    {
      id: 'openai/gpt-4o-mini',
      name: 'GPT-4o Mini (OpenRouter)',
      provider: 'openrouter',
      costPer1kTokens: 0.15,
      maxTokens: 128000,
      description: 'GPT-4o Mini via OpenRouter',
      recommended: ['outline', 'content']
    }
  ]
}

export const DEFAULT_AI_SETTINGS: Omit<AISettings, 'id' | 'user_id' | 'created_at' | 'updated_at'> = {
  provider: 'gemini',
  models: {
    research: 'gemini-1.5-flash',
    outline: 'gemini-1.5-pro',
    content: 'gemini-1.5-pro',
    metadata: 'gemini-1.5-flash'
  },
  preferences: {
    maxTokensPerRequest: 4000,
    temperature: 0.7,
    enableCaching: true,
    costLimit: 10.0 // $10 USD limit
  }
}
