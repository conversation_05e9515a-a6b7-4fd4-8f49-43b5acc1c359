// External Citation System
'use client'

import { ResearchSource, AIRequest, AISettings } from '@/types/ai'
import { optimizedAIManager } from './optimization'

export interface Citation {
  id: string
  source: ResearchSource
  citationType: 'inline' | 'reference' | 'footnote'
  citationText: string
  context: string
  position: number
  relevanceScore: number
}

export interface CitationStyle {
  format: 'apa' | 'mla' | 'chicago' | 'ieee' | 'web'
  includeAccessDate: boolean
  useShortUrls: boolean
  groupSimilarSources: boolean
  maxCitationsPerSection: number
}

export interface CitationContext {
  content: string
  claims: string[]
  statistics: string[]
  quotes: string[]
  factualStatements: string[]
}

// Citation Engine
export class CitationEngine {
  private static instance: CitationEngine

  static getInstance(): CitationEngine {
    if (!CitationEngine.instance) {
      CitationEngine.instance = new CitationEngine()
    }
    return CitationEngine.instance
  }

  // Main method to add citations to content
  async addCitations(
    content: string,
    sources: ResearchSource[],
    style: CitationStyle = {
      format: 'web',
      includeAccessDate: true,
      useShortUrls: false,
      groupSimilarSources: true,
      maxCitationsPerSection: 5
    },
    settings: AISettings,
    userId: string
  ): Promise<{ content: string; bibliography: string }> {
    try {
      // Analyze content for citation opportunities
      const citationContext = await this.analyzeContentForCitations(content, settings, userId)
      
      // Find citation opportunities
      const citations = await this.findCitationOpportunities(
        citationContext,
        sources,
        style
      )

      // Insert citations into content
      const citedContent = this.insertCitations(content, citations, style)
      
      // Generate bibliography
      const bibliography = this.generateBibliography(citations, style)

      return {
        content: citedContent,
        bibliography
      }

    } catch (error) {
      console.error('Citation system failed:', error)
      return {
        content,
        bibliography: this.generateFallbackBibliography(sources, style)
      }
    }
  }

  private async analyzeContentForCitations(
    content: string,
    settings: AISettings,
    userId: string
  ): Promise<CitationContext> {
    const analysisPrompt = `
Analyze this content and identify statements that would benefit from citations:

Content: ${content.substring(0, 2000)}...

Identify and extract:
1. Factual claims that need verification
2. Statistics or data points
3. Expert quotes or opinions
4. Technical specifications
5. Research findings or studies mentioned

Return a JSON object with:
{
  "claims": ["factual claim 1", "factual claim 2"],
  "statistics": ["statistic 1", "statistic 2"],
  "quotes": ["quote 1", "quote 2"],
  "factualStatements": ["statement 1", "statement 2"]
}

Focus on statements that would benefit from authoritative sources.
`

    const request: AIRequest = {
      provider: settings.provider,
      model: settings.models.content,
      prompt: analysisPrompt,
      maxTokens: 1000,
      temperature: 0.2,
      systemPrompt: 'You are a research analyst identifying content that needs citations for credibility.'
    }

    try {
      const response = await optimizedAIManager.generateOptimizedResponse(request, userId, settings)
      const analysis = JSON.parse(response.content)
      
      return {
        content,
        claims: analysis.claims || [],
        statistics: analysis.statistics || [],
        quotes: analysis.quotes || [],
        factualStatements: analysis.factualStatements || []
      }
    } catch (error) {
      console.error('Failed to analyze content for citations:', error)
      return {
        content,
        claims: [],
        statistics: [],
        quotes: [],
        factualStatements: []
      }
    }
  }

  private async findCitationOpportunities(
    context: CitationContext,
    sources: ResearchSource[],
    style: CitationStyle
  ): Promise<Citation[]> {
    const opportunities: Citation[] = []
    const allStatements = [
      ...context.claims,
      ...context.statistics,
      ...context.quotes,
      ...context.factualStatements
    ]

    for (const statement of allStatements) {
      const relevantSources = this.findRelevantSources(statement, sources)
      
      for (const source of relevantSources) {
        const citation = this.createCitation(statement, source, context.content, style)
        if (citation) {
          opportunities.push(citation)
        }
      }
    }

    // Sort by relevance and limit per section
    return opportunities
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, style.maxCitationsPerSection * 3)
  }

  private findRelevantSources(statement: string, sources: ResearchSource[]): ResearchSource[] {
    return sources
      .map(source => ({
        source,
        relevance: this.calculateSourceRelevance(statement, source)
      }))
      .filter(item => item.relevance > 0.3)
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, 3)
      .map(item => item.source)
  }

  private calculateSourceRelevance(statement: string, source: ResearchSource): number {
    const statementLower = statement.toLowerCase()
    const sourceText = `${source.title} ${source.content}`.toLowerCase()
    
    let score = 0

    // Check for keyword overlap
    const statementWords = statementLower.split(/\s+/).filter(word => word.length > 3)
    const matchingWords = statementWords.filter(word => sourceText.includes(word))
    score += (matchingWords.length / statementWords.length) * 0.5

    // Boost for authoritative domains
    const authoritativeDomains = ['.edu', '.gov', '.org', 'research', 'study', 'journal']
    if (authoritativeDomains.some(domain => source.domain.includes(domain))) {
      score += 0.3
    }

    // Boost for recent content
    if (source.datePublished) {
      const daysSincePublished = (Date.now() - new Date(source.datePublished).getTime()) / (1000 * 60 * 60 * 24)
      if (daysSincePublished < 365) {
        score += 0.2
      }
    }

    return Math.min(score, 1.0)
  }

  private createCitation(
    statement: string,
    source: ResearchSource,
    content: string,
    style: CitationStyle
  ): Citation | null {
    const position = content.indexOf(statement)
    if (position === -1) return null

    const citationId = this.generateCitationId(source)
    const citationText = this.formatCitation(source, style)

    return {
      id: citationId,
      source,
      citationType: 'inline',
      citationText,
      context: statement,
      position,
      relevanceScore: this.calculateSourceRelevance(statement, source)
    }
  }

  private insertCitations(content: string, citations: Citation[], style: CitationStyle): string {
    let citedContent = content
    const citationMap = new Map<string, number>()
    let citationCounter = 1

    // Sort citations by position (reverse order to maintain positions)
    const sortedCitations = citations.sort((a, b) => b.position - a.position)

    for (const citation of sortedCitations) {
      // Get or assign citation number
      if (!citationMap.has(citation.id)) {
        citationMap.set(citation.id, citationCounter++)
      }
      const citationNumber = citationMap.get(citation.id)!

      // Find the statement in content
      const statementIndex = citedContent.indexOf(citation.context)
      if (statementIndex === -1) continue

      // Insert citation based on style
      const citationMark = this.formatCitationMark(citation, citationNumber, style)
      const insertPosition = statementIndex + citation.context.length

      citedContent = 
        citedContent.slice(0, insertPosition) + 
        citationMark + 
        citedContent.slice(insertPosition)
    }

    return citedContent
  }

  private formatCitationMark(citation: Citation, number: number, style: CitationStyle): string {
    switch (style.format) {
      case 'apa':
        return ` (${citation.source.author || 'Author'}, ${this.getYear(citation.source.datePublished)})`
      case 'mla':
        return ` (${citation.source.author || 'Author'})`
      case 'chicago':
        return `<sup>${number}</sup>`
      case 'ieee':
        return ` [${number}]`
      case 'web':
      default:
        return ` [[${number}]](${citation.source.url})`
    }
  }

  private generateBibliography(citations: Citation[], style: CitationStyle): string {
    if (citations.length === 0) return ''

    const uniqueSources = new Map<string, ResearchSource>()
    citations.forEach(citation => {
      uniqueSources.set(citation.id, citation.source)
    })

    let bibliography = '\n## References\n\n'
    let counter = 1

    for (const [id, source] of uniqueSources) {
      const formattedCitation = this.formatBibliographyEntry(source, counter, style)
      bibliography += `${formattedCitation}\n\n`
      counter++
    }

    return bibliography
  }

  private formatBibliographyEntry(source: ResearchSource, number: number, style: CitationStyle): string {
    const author = source.author || 'Unknown Author'
    const title = source.title
    const url = style.useShortUrls ? this.shortenUrl(source.url) : source.url
    const date = source.datePublished ? this.formatDate(source.datePublished, style) : 'n.d.'
    const accessDate = style.includeAccessDate ? ` Accessed ${this.formatDate(new Date().toISOString(), style)}.` : ''

    switch (style.format) {
      case 'apa':
        return `${author}. (${this.getYear(source.datePublished)}). *${title}*. Retrieved from ${url}${accessDate}`
      
      case 'mla':
        return `${author}. "${title}." *Web*. ${date}. <${url}>.${accessDate}`
      
      case 'chicago':
        return `${number}. ${author}, "${title}," accessed ${this.formatDate(new Date().toISOString(), style)}, ${url}.`
      
      case 'ieee':
        return `[${number}] ${author}, "${title}," ${source.domain}, ${date}. [Online]. Available: ${url}${accessDate}`
      
      case 'web':
      default:
        return `${number}. **${title}** - ${author}. *${source.domain}*. ${date}. [Link](${url})${accessDate}`
    }
  }

  private generateFallbackBibliography(sources: ResearchSource[], style: CitationStyle): string {
    if (sources.length === 0) return ''

    let bibliography = '\n## References\n\n'
    
    sources.slice(0, 10).forEach((source, index) => {
      const formattedCitation = this.formatBibliographyEntry(source, index + 1, style)
      bibliography += `${formattedCitation}\n\n`
    })

    return bibliography
  }

  private generateCitationId(source: ResearchSource): string {
    return btoa(source.url).replace(/[^a-zA-Z0-9]/g, '').substring(0, 8)
  }

  private getYear(dateString?: string): string {
    if (!dateString) return 'n.d.'
    return new Date(dateString).getFullYear().toString()
  }

  private formatDate(dateString: string, style: CitationStyle): string {
    const date = new Date(dateString)
    
    switch (style.format) {
      case 'apa':
      case 'chicago':
        return date.toLocaleDateString('en-US', { 
          year: 'numeric', 
          month: 'long', 
          day: 'numeric' 
        })
      case 'mla':
        return date.toLocaleDateString('en-US', { 
          day: 'numeric',
          month: 'short', 
          year: 'numeric'
        })
      case 'ieee':
      case 'web':
      default:
        return date.toLocaleDateString('en-US', { 
          year: 'numeric', 
          month: 'short', 
          day: 'numeric' 
        })
    }
  }

  private shortenUrl(url: string): string {
    try {
      const urlObj = new URL(url)
      return `${urlObj.hostname}${urlObj.pathname.substring(0, 30)}...`
    } catch {
      return url.substring(0, 50) + '...'
    }
  }

  // Validate citations for accuracy and relevance
  validateCitations(citations: Citation[]): { valid: Citation[]; issues: string[] } {
    const valid: Citation[] = []
    const issues: string[] = []

    citations.forEach((citation, index) => {
      // Check URL validity
      try {
        new URL(citation.source.url)
      } catch {
        issues.push(`Citation ${index + 1}: Invalid URL`)
        return
      }

      // Check relevance score
      if (citation.relevanceScore < 0.3) {
        issues.push(`Citation ${index + 1}: Low relevance score`)
        return
      }

      // Check for duplicate sources
      const duplicates = citations.filter(c => c.source.url === citation.source.url)
      if (duplicates.length > 1) {
        issues.push(`Citation ${index + 1}: Duplicate source`)
      }

      valid.push(citation)
    })

    return { valid, issues }
  }
}

// Export singleton instance
export const citationEngine = CitationEngine.getInstance()

// Utility functions
export const addExternalCitations = async (
  content: string,
  sources: ResearchSource[],
  settings: AISettings,
  userId: string,
  style?: CitationStyle
): Promise<{ content: string; bibliography: string }> => {
  return await citationEngine.addCitations(content, sources, style, settings, userId)
}

export const validateCitations = (citations: Citation[]) => {
  return citationEngine.validateCitations(citations)
}

export const formatBibliography = (sources: ResearchSource[], style: CitationStyle = { format: 'web', includeAccessDate: true, useShortUrls: false, groupSimilarSources: true, maxCitationsPerSection: 5 }): string => {
  return citationEngine['generateFallbackBibliography'](sources, style)
}
