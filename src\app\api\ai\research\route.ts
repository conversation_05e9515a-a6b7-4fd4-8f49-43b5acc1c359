// AI Research API Endpoint
import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { conductKeywordResearch, getOrCreateAISettings } from '@/lib/ai'
import { verifyIdToken } from '@/lib/firebase-admin'

// Rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

// Rate limiting configuration
const RATE_LIMIT = {
  maxRequests: 10, // requests per window
  windowMs: 60 * 60 * 1000, // 1 hour
}

function checkRateLimit(userId: string): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now()
  const userLimit = rateLimitStore.get(userId)

  if (!userLimit || now > userLimit.resetTime) {
    // Reset or create new limit
    const resetTime = now + RATE_LIMIT.windowMs
    rateLimitStore.set(userId, { count: 1, resetTime })
    return { allowed: true, remaining: RATE_LIMIT.maxRequests - 1, resetTime }
  }

  if (userLimit.count >= RATE_LIMIT.maxRequests) {
    return { allowed: false, remaining: 0, resetTime: userLimit.resetTime }
  }

  // Increment count
  userLimit.count++
  rateLimitStore.set(userId, userLimit)
  
  return { 
    allowed: true, 
    remaining: RATE_LIMIT.maxRequests - userLimit.count, 
    resetTime: userLimit.resetTime 
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const headersList = headers()
    const authorization = headersList.get('authorization')
    
    if (!authorization?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401 }
      )
    }

    const token = authorization.split('Bearer ')[1]
    
    // Verify Firebase token
    const decodedToken = await verifyIdToken(token)
    const userId = decodedToken.uid

    // Check rate limiting
    const rateLimit = checkRateLimit(userId)
    if (!rateLimit.allowed) {
      return NextResponse.json(
        { 
          error: 'Rate limit exceeded',
          resetTime: rateLimit.resetTime
        },
        { 
          status: 429,
          headers: {
            'X-RateLimit-Limit': RATE_LIMIT.maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': rateLimit.resetTime.toString()
          }
        }
      )
    }

    // Parse request body
    const body = await request.json()
    const { keywords, options = {} } = body

    // Validate input
    if (!keywords || !Array.isArray(keywords) || keywords.length === 0) {
      return NextResponse.json(
        { error: 'Keywords array is required and cannot be empty' },
        { status: 400 }
      )
    }

    if (keywords.length > 10) {
      return NextResponse.json(
        { error: 'Maximum 10 keywords allowed' },
        { status: 400 }
      )
    }

    // Validate keywords
    for (const keyword of keywords) {
      if (typeof keyword !== 'string' || keyword.trim().length < 2) {
        return NextResponse.json(
          { error: 'Each keyword must be a string with at least 2 characters' },
          { status: 400 }
        )
      }
    }

    // Get user AI settings
    const settings = await getOrCreateAISettings(userId)

    // Check cost limits
    const monthlyUsage = 0 // TODO: Implement actual usage tracking
    if (monthlyUsage >= settings.preferences.costLimit) {
      return NextResponse.json(
        { 
          error: 'Monthly cost limit exceeded',
          currentUsage: monthlyUsage,
          limit: settings.preferences.costLimit
        },
        { status: 402 }
      )
    }

    // Conduct research
    const researchOptions = {
      maxSources: Math.min(options.maxSources || 10, 20),
      includeAcademic: options.includeAcademic || false,
      includeNews: options.includeNews !== false, // default true
      includeBlog: options.includeBlog !== false, // default true
      language: options.language || 'en',
      targetAudience: options.targetAudience || 'intermediate developers'
    }

    const result = await conductKeywordResearch(keywords, userId, researchOptions)

    // Return response with rate limit headers
    return NextResponse.json(
      {
        success: true,
        data: result,
        metadata: {
          tokensUsed: result.tokensUsed,
          cost: result.cost,
          timestamp: new Date().toISOString()
        }
      },
      {
        status: 200,
        headers: {
          'X-RateLimit-Limit': RATE_LIMIT.maxRequests.toString(),
          'X-RateLimit-Remaining': rateLimit.remaining.toString(),
          'X-RateLimit-Reset': rateLimit.resetTime.toString()
        }
      }
    )

  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('AI Research API Error:', error)
    }

    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('Cost limit exceeded')) {
        return NextResponse.json(
          { error: 'Cost limit exceeded', details: error.message },
          { status: 402 }
        )
      }
      
      if (error.message.includes('Invalid token')) {
        return NextResponse.json(
          { error: 'Invalid authentication token' },
          { status: 401 }
        )
      }

      if (error.message.includes('Research failed')) {
        return NextResponse.json(
          { error: 'Research operation failed', details: error.message },
          { status: 500 }
        )
      }
    }

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: process.env.NODE_ENV === 'development' ? (error as Error).message : 'An unexpected error occurred'
      },
      { status: 500 }
    )
  }
}

// GET endpoint for research status/history
export async function GET(request: NextRequest) {
  try {
    const headersList = headers()
    const authorization = headersList.get('authorization')
    
    if (!authorization?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401 }
      )
    }

    const token = authorization.split('Bearer ')[1]
    const decodedToken = await verifyIdToken(token)
    const userId = decodedToken.uid

    // Get rate limit status
    const rateLimit = checkRateLimit(userId)
    
    return NextResponse.json({
      rateLimit: {
        limit: RATE_LIMIT.maxRequests,
        remaining: rateLimit.remaining,
        resetTime: rateLimit.resetTime
      },
      status: 'ready'
    })

  } catch (error) {
    console.error('AI Research Status Error:', error)
    return NextResponse.json(
      { error: 'Failed to get research status' },
      { status: 500 }
    )
  }
}
