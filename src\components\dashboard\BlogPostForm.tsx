'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/providers/AuthProvider'
import { createBlogPost, updateBlogPost, getBlogPost } from '@/lib/firebase-operations'
import { DatabaseBlogPost } from '@/types'
import { processMarkdownContent } from '@/lib/markdown-client'
import { sanitizeBlogContent } from '@/lib/sanitize'
import FeaturedImageSelector from './FeaturedImageSelector'

interface BlogPostFormProps {
  postId?: string
  mode: 'create' | 'edit'
}

export default function BlogPostForm({ postId, mode }: BlogPostFormProps) {
  const { user } = useAuth()
  const router = useRouter()
  
  // Get current date in the format needed for datetime-local input
  const getCurrentDateTime = () => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hours = String(now.getHours()).padStart(2, '0')
    const minutes = String(now.getMinutes()).padStart(2, '0')
    return `${year}-${month}-${day}T${hours}:${minutes}`
  }

  const [formData, setFormData] = useState({
    title: '',
    excerpt: '',
    content: '',
    featured_image: '',
    published: false,
    scheduled_for: getCurrentDateTime(),
    tags: [] as string[],
    categories: [] as string[],
  })

  const [tagsInput, setTagsInput] = useState('')
  const [categoriesInput, setCategoriesInput] = useState('')
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [previewContent, setPreviewContent] = useState('')

  useEffect(() => {
    if (mode === 'edit' && postId) {
      loadPost()
    }
  }, [mode, postId])

  const loadPost = async () => {
    if (!postId) return

    setLoading(true)
    try {
      const post = await getBlogPost(postId)
      if (post) {
        setFormData({
          title: post.title,
          excerpt: post.excerpt || '',
          content: post.content,
          featured_image: post.featured_image || '',
          published: post.published,
          scheduled_for: post.scheduled_for || getCurrentDateTime(),
          tags: post.tags || [],
          categories: post.categories || [],
        })
        // Set the comma-separated input values
        setTagsInput((post.tags || []).join(', '))
        setCategoriesInput((post.categories || []).join(', '))
      }
    } catch (error) {
      console.error('Error loading post:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
  }

  const handleTagsChange = (value: string) => {
    setTagsInput(value)
    // Convert comma-separated string to array, trim whitespace, and filter empty strings
    const tagsArray = value.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
    setFormData(prev => ({
      ...prev,
      tags: tagsArray
    }))
  }

  const handleCategoriesChange = (value: string) => {
    setCategoriesInput(value)
    // Convert comma-separated string to array, trim whitespace, and filter empty strings
    const categoriesArray = value.split(',').map(category => category.trim()).filter(category => category.length > 0)
    setFormData(prev => ({
      ...prev,
      categories: categoriesArray
    }))
  }

  const handlePreview = async () => {
    if (formData.content) {
      const processed = await processMarkdownContent(formData.content)
      // Sanitize HTML content to prevent XSS
      const sanitizedContent = sanitizeBlogContent(processed)
      setPreviewContent(sanitizedContent)
    }
    setShowPreview(!showPreview)
  }

  const handleSubmit = async (e: React.FormEvent, isDraft = false) => {
    e.preventDefault()
    if (!user) return

    setSaving(true)
    try {
      // Determine if post should be published based on scheduled date
      const scheduledDate = new Date(formData.scheduled_for)
      const now = new Date()
      const shouldPublish = !isDraft && scheduledDate <= now

      const postData = {
        ...formData,
        published: shouldPublish,
      }

      if (mode === 'create') {
        const newPostId = await createBlogPost(postData, user.uid)
        router.push(`/dashboard/posts/${newPostId}/edit`)
      } else if (postId) {
        await updateBlogPost(postId, postData)
        router.push('/dashboard/posts')
      }
    } catch (error) {
      console.error('Error saving post:', error)
      alert('Failed to save post')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      <form onSubmit={(e) => handleSubmit(e)} className="space-y-6">
        {/* Title */}
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
            Title *
          </label>
          <input
            type="text"
            id="title"
            name="title"
            required
            value={formData.title}
            onChange={handleInputChange}
            className="w-full px-4 py-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-slate-700 dark:text-slate-100 transition-colors placeholder:text-sm"
            placeholder="Post title"
          />
        </div>

        {/* Excerpt */}
        <div>
          <label htmlFor="excerpt" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Excerpt
          </label>
          <textarea
            id="excerpt"
            name="excerpt"
            rows={3}
            value={formData.excerpt}
            onChange={handleInputChange}
            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder:text-sm"
            placeholder="Brief post description"
          />
        </div>

        {/* Featured Image */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Featured Image
          </label>
          <FeaturedImageSelector
            selectedImage={formData.featured_image}
            onImageSelect={(imageUrl) => setFormData(prev => ({ ...prev, featured_image: imageUrl }))}
          />
        </div>

        {/* Tags, Categories, and Date - All in One Line */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          {/* Tags */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Tags
            </label>
            <input
              type="text"
              value={tagsInput}
              onChange={(e) => handleTagsChange(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder:text-sm"
              placeholder="technology, tutorial, javascript"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Separate multiple tags with commas
            </p>
          </div>

          {/* Categories */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Categories
            </label>
            <input
              type="text"
              value={categoriesInput}
              onChange={(e) => handleCategoriesChange(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder:text-sm"
              placeholder="web development, programming, design"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Separate multiple categories with commas
            </p>
          </div>

          {/* Publish Date */}
          <div>
            <label htmlFor="scheduled_for" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Publish Date
            </label>
            <input
              type="datetime-local"
              id="scheduled_for"
              name="scheduled_for"
              value={formData.scheduled_for}
              onChange={handleInputChange}
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder:text-sm [&::-webkit-datetime-edit]:text-sm [&::-webkit-calendar-picker-indicator]:text-sm"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Set when this post should be published
            </p>
          </div>
        </div>

        {/* Content */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <label htmlFor="content" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Content * (Markdown supported)
            </label>
            <button
              type="button"
              onClick={handlePreview}
              className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
            >
              {showPreview ? 'Hide Preview' : 'Show Preview'}
            </button>
          </div>
          
          <div className={`grid gap-4 ${showPreview ? 'grid-cols-1 lg:grid-cols-3' : 'grid-cols-1'}`}>
            <div className={showPreview ? 'lg:col-span-2' : ''}>
              <textarea
                id="content"
                name="content"
                required
                rows={20}
                value={formData.content}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white font-mono text-sm placeholder:text-sm"
                placeholder="Write your post content in Markdown"
              />
            </div>

            {showPreview && (
              <div className="border border-gray-300 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Preview</h4>
                <div
                  className="prose dark:prose-invert max-w-none text-sm"
                  dangerouslySetInnerHTML={{ __html: previewContent }}
                />
              </div>
            )}
          </div>
        </div>



        {/* Actions */}
        <div className="flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
          >
            Cancel
          </button>
          
          <div className="flex gap-3">
            <button
              type="button"
              onClick={(e) => handleSubmit(e, true)}
              disabled={saving}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
            >
              Save as Draft
            </button>
            <button
              type="submit"
              disabled={saving}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {saving ? 'Saving...' : mode === 'create' ? 'Create Post' : 'Update Post'}
            </button>
          </div>
        </div>
      </form>
    </div>
  )
}
