'use client'

import { Moon, Sun } from 'lucide-react'
import { useTheme } from '@/components/providers/ThemeProvider'

export function DashboardThemeToggle() {
  const { theme, setTheme } = useTheme()

  const toggleTheme = () => {
    if (theme === 'light') {
      setTheme('dark')
    } else if (theme === 'dark') {
      setTheme('light')
    } else {
      // If system, toggle to the opposite of current system preference
      if (typeof window !== 'undefined') {
        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
        setTheme(systemTheme === 'dark' ? 'light' : 'dark')
      } else {
        setTheme('light')
      }
    }
  }

  const getCurrentTheme = () => {
    if (theme === 'system') {
      if (typeof window !== 'undefined') {
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
      }
      return 'light'
    }
    return theme
  }

  return (
    <button
      onClick={toggleTheme}
      className="inline-flex items-center px-3 py-2 dashboard-text hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors shadow-sm font-medium"
      title={`Switch to ${getCurrentTheme() === 'dark' ? 'light' : 'dark'} mode`}
    >
      {getCurrentTheme() === 'dark' ? (
        <>
          <Sun className="w-4 h-4 mr-2" />
          Light
        </>
      ) : (
        <>
          <Moon className="w-4 h-4 mr-2" />
          Dark
        </>
      )}
    </button>
  )
}
