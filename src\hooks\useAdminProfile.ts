'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { getAdminProfile, updateAdminProfile } from '@/lib/firebase-operations'
import { GoogleAuthProvider, signInWithPopup } from 'firebase/auth'
import { auth } from '@/lib/firebase'

const ADMIN_UID = 'KNlrg408xubJeEmwFpUbeDQWBgF3'

interface AdminProfile {
  displayName?: string
  photoURL?: string
  email?: string
  updated_at?: any
}

export function useAdminProfile() {
  const [adminProfile, setAdminProfile] = useState<AdminProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const { user } = useAuth()

  // Fetch admin profile from Firestore
  useEffect(() => {
    const fetchAdminProfile = async () => {
      try {
        const profile = await getAdminProfile()
        setAdminProfile(profile)
      } catch (error) {
        console.error('Error fetching admin profile:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchAdminProfile()
  }, [])

  // Update admin profile when admin user logs in with fresh data
  useEffect(() => {
    const updateProfileIfAdmin = async () => {
      if (user && user.uid === ADMIN_UID) {
        try {
          // Check if profile data has changed
          const hasChanged = !adminProfile ||
            adminProfile.photoURL !== user.photoURL ||
            adminProfile.displayName !== user.displayName ||
            adminProfile.email !== user.email

          if (hasChanged) {
            // Update the stored profile with current user data
            await updateAdminProfile(user.uid, {
              displayName: user.displayName || undefined,
              photoURL: user.photoURL || undefined,
              email: user.email || undefined,
            })

            // Update local state
            setAdminProfile({
              displayName: user.displayName || undefined,
              photoURL: user.photoURL || undefined,
              email: user.email || undefined,
            })
          }
        } catch (error) {
          console.error('Error updating admin profile:', error)
        }
      }
    }

    if (!loading && user) {
      updateProfileIfAdmin()
    }
  }, [user, loading, adminProfile])

  // Periodically check for profile updates when admin is logged in
  useEffect(() => {
    if (!user || user.uid !== ADMIN_UID) return

    const checkForUpdates = async () => {
      try {
        // Reload user data to get latest profile info
        await user.reload()

        // Check if profile data has changed
        const hasChanged = !adminProfile ||
          adminProfile.photoURL !== user.photoURL ||
          adminProfile.displayName !== user.displayName

        if (hasChanged) {
          // Update the stored profile with fresh user data
          await updateAdminProfile(user.uid, {
            displayName: user.displayName || undefined,
            photoURL: user.photoURL || undefined,
            email: user.email || undefined,
          })

          // Update local state
          setAdminProfile({
            displayName: user.displayName || undefined,
            photoURL: user.photoURL || undefined,
            email: user.email || undefined,
          })
        }
      } catch (error) {
        console.error('Error checking for profile updates:', error)
      }
    }

    // Check for updates every 5 minutes
    const interval = setInterval(checkForUpdates, 5 * 60 * 1000)

    return () => clearInterval(interval)
  }, [user, adminProfile])

  // Get the best available avatar source
  const getAvatarSrc = () => {
    // If admin is currently logged in, use their live photoURL
    if (user?.uid === ADMIN_UID && user?.photoURL) {
      return user.photoURL
    }
    
    // Otherwise, use cached admin profile photoURL
    if (adminProfile?.photoURL) {
      return adminProfile.photoURL
    }
    
    // Fallback to static avatar
    return "/images/avatar.jpg"
  }

  // Get the best available display name
  const getDisplayName = () => {
    // If admin is currently logged in, use their live displayName
    if (user?.uid === ADMIN_UID && user?.displayName) {
      return user.displayName
    }
    
    // Otherwise, use cached admin profile displayName
    if (adminProfile?.displayName) {
      return adminProfile.displayName
    }
    
    // Fallback to default name
    return "Ernst Romelo"
  }

  // Function to manually refresh profile data
  const refreshProfile = async () => {
    if (!user || user.uid !== ADMIN_UID) return

    setRefreshing(true)
    try {
      // Force a fresh authentication with Google to get the latest profile data
      const provider = new GoogleAuthProvider()
      provider.setCustomParameters({
        prompt: 'none' // Don't show login UI, just refresh the token
      })

      // Re-authenticate to get fresh profile data
      const result = await signInWithPopup(auth, provider)
      const freshUser = result.user

      // Update the stored profile with fresh user data
      await updateAdminProfile(freshUser.uid, {
        displayName: freshUser.displayName || undefined,
        photoURL: freshUser.photoURL || undefined,
        email: freshUser.email || undefined,
      })

      // Update local state
      setAdminProfile({
        displayName: freshUser.displayName || undefined,
        photoURL: freshUser.photoURL || undefined,
        email: freshUser.email || undefined,
      })
    } catch (error) {
      console.error('Error refreshing admin profile:', error)
      // Fallback to regular reload if re-auth fails
      try {
        await user.reload()
        await updateAdminProfile(user.uid, {
          displayName: user.displayName || undefined,
          photoURL: user.photoURL || undefined,
          email: user.email || undefined,
        })

        setAdminProfile({
          displayName: user.displayName || undefined,
          photoURL: user.photoURL || undefined,
          email: user.email || undefined,
        })
      } catch (fallbackError) {
        console.error('Fallback refresh also failed:', fallbackError)
      }
    } finally {
      setRefreshing(false)
    }
  }

  return {
    adminProfile,
    loading,
    refreshing,
    avatarSrc: getAvatarSrc(),
    displayName: getDisplayName(),
    isCurrentlyAdmin: user?.uid === ADMIN_UID,
    refreshProfile,
  }
}
