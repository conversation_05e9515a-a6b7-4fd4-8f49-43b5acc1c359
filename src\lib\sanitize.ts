'use client'

import DOMPurify from 'dompurify'

// Configure DOMPurify for safe HTML sanitization
const configureDOMPurify = () => {
  if (typeof window !== 'undefined') {
    // Clear any existing hooks
    DOMPurify.removeAllHooks()

    // Add aggressive sanitization hooks
    DOMPurify.addHook('beforeSanitizeElements', (node) => {
      // Remove any script tags completely
      if (node.tagName === 'SCRIPT') {
        node.remove()
        return node
      }
      // Remove any elements with event handlers
      if (node.hasAttributes && node.hasAttributes()) {
        const attrs = node.attributes
        for (let i = attrs.length - 1; i >= 0; i--) {
          const attr = attrs[i]
          if (attr.name.startsWith('on')) {
            node.removeAttribute(attr.name)
          }
        }
      }
    })

    DOMPurify.addHook('afterSanitizeAttributes', (node) => {
      // Remove javascript: and vbscript: from all attributes
      if (node.hasAttributes && node.hasAttributes()) {
        const attrs = node.attributes
        for (let i = 0; i < attrs.length; i++) {
          const attr = attrs[i]
          if (attr.value) {
            attr.value = attr.value.replace(/javascript:/gi, '')
                                   .replace(/vbscript:/gi, '')
                                   .replace(/data:text\/html/gi, '')
          }
        }
      }
    })

    // Configure allowed tags and attributes
    const config = {
      ALLOWED_TAGS: [
        // Text formatting
        'p', 'br', 'strong', 'b', 'em', 'i', 'u', 'strike', 'del', 'ins',
        // Headings
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        // Lists
        'ul', 'ol', 'li',
        // Links and media
        'a', 'img',
        // Code
        'code', 'pre',
        // Tables
        'table', 'thead', 'tbody', 'tr', 'th', 'td',
        // Quotes and divs
        'blockquote', 'div', 'span',
        // Line breaks
        'hr'
      ],
      ALLOWED_ATTR: [
        // Links
        'href', 'title', 'target', 'rel',
        // Images
        'src', 'alt', 'width', 'height',
        // General
        'class', 'id',
        // Code highlighting
        'data-language'
      ],
      ALLOWED_URI_REGEXP: /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i,
      // Strictly forbid dangerous attributes and tags
      FORBID_ATTR: ['onerror', 'onload', 'onclick', 'onmouseover', 'onfocus', 'onblur', 'onmouseout', 'onkeydown', 'onkeyup', 'onkeypress'],
      FORBID_TAGS: ['script', 'object', 'embed', 'form', 'input', 'button', 'iframe', 'frame', 'frameset'],
      // Don't keep content of forbidden elements
      KEEP_CONTENT: false,
      // Remove empty and dangerous elements
      REMOVE_EMPTY: ['script', 'style', 'iframe'],
      // Disable data attributes completely
      ALLOW_DATA_ATTR: false,
      // Force removal of unknown protocols
      SANITIZE_DOM: true
    }

    return config
  }
  return {}
}

/**
 * Sanitize HTML content to prevent XSS attacks
 * @param html - Raw HTML string to sanitize
 * @param options - Optional DOMPurify configuration overrides
 * @returns Sanitized HTML string safe for rendering
 */
export function sanitizeHtml(html: string, options?: any): string {
  if (typeof window === 'undefined') {
    // Server-side: aggressive sanitization
    return html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
      .replace(/<embed[^>]*>/gi, '')
      .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/vbscript:/gi, '')
      .replace(/data:text\/html/gi, '')
  }

  const config = { ...configureDOMPurify(), ...options }
  const sanitized = DOMPurify.sanitize(html, config)

  // Additional client-side checks
  if (sanitized.includes('<script') || sanitized.includes('javascript:') || sanitized.includes('onerror')) {
    return sanitized.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                    .replace(/javascript:/gi, '')
                    .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '')
  }

  return sanitized
}

/**
 * Sanitize HTML specifically for blog content
 * Allows more formatting tags but still prevents XSS
 */
export function sanitizeBlogContent(html: string): string {
  return sanitizeHtml(html, {
    ALLOWED_TAGS: [
      'p', 'br', 'strong', 'b', 'em', 'i', 'u', 'strike', 'del', 'ins',
      'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'ul', 'ol', 'li',
      'a', 'img',
      'code', 'pre',
      'table', 'thead', 'tbody', 'tr', 'th', 'td',
      'blockquote', 'div', 'span',
      'hr'
    ],
    ALLOWED_ATTR: [
      'href', 'title', 'target', 'rel',
      'src', 'alt', 'width', 'height',
      'class', 'id',
      'data-language'
    ]
  })
}

/**
 * Sanitize HTML for comments (more restrictive)
 * Only allows basic formatting
 */
export function sanitizeCommentContent(html: string): string {
  return sanitizeHtml(html, {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'b', 'em', 'i', 'a'],
    ALLOWED_ATTR: ['href', 'title'],
    FORBID_TAGS: ['script', 'img', 'video', 'audio', 'iframe', 'object', 'embed']
  })
}

/**
 * Strip all HTML tags and return plain text
 * Use for titles, excerpts, and other text-only content
 */
export function stripHtml(html: string): string {
  if (typeof window === 'undefined') {
    return html.replace(/<[^>]*>/g, '')
  }
  
  return DOMPurify.sanitize(html, { 
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true
  })
}

/**
 * Escape HTML entities for safe display
 * Use when you want to show HTML code as text
 */
export function escapeHtml(text: string): string {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}

/**
 * Validate if content contains potentially dangerous elements
 * Returns true if content appears safe, false if suspicious
 */
export function validateContentSafety(content: string): boolean {
  const dangerousPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+\s*=/i, // Event handlers like onclick, onload
    /<iframe/i,
    /<object/i,
    /<embed/i,
    /data:text\/html/i,
    /vbscript:/i
  ]

  return !dangerousPatterns.some(pattern => pattern.test(content))
}

/**
 * Safe component for rendering sanitized HTML
 * Use this instead of dangerouslySetInnerHTML
 */
export function createSafeHTML(html: string, sanitizer = sanitizeHtml) {
  return { __html: sanitizer(html) }
}

// Export configured DOMPurify instance for advanced usage
export { DOMPurify }

// Default export
export default {
  sanitizeHtml,
  sanitizeBlogContent,
  sanitizeCommentContent,
  stripHtml,
  escapeHtml,
  validateContentSafety,
  createSafeHTML
}
