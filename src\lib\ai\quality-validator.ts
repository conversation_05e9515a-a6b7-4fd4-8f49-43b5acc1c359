// Content Quality Validation System
'use client'

import { AIRequest, AISettings } from '@/types/ai'
import { optimizedAIManager } from './optimization'

export interface QualityScore {
  overall: number // 0-100
  readability: number
  seo: number
  engagement: number
  structure: number
  accuracy: number
}

export interface QualityIssue {
  type: 'error' | 'warning' | 'suggestion'
  category: 'readability' | 'seo' | 'engagement' | 'structure' | 'accuracy'
  message: string
  suggestion: string
  severity: 'low' | 'medium' | 'high'
  location?: string
}

export interface ValidationResult {
  score: QualityScore
  issues: QualityIssue[]
  recommendations: string[]
  isPublishReady: boolean
  estimatedImprovements: {
    readability: string[]
    seo: string[]
    engagement: string[]
  }
}

export interface ValidationOptions {
  strictMode: boolean
  targetAudience: 'beginner' | 'intermediate' | 'advanced'
  contentType: 'tutorial' | 'guide' | 'analysis' | 'opinion' | 'news'
  minWordCount: number
  maxWordCount: number
  requireImages: boolean
  requireCodeExamples: boolean
}

// Quality Validator Class
export class QualityValidator {
  private static instance: QualityValidator

  static getInstance(): QualityValidator {
    if (!QualityValidator.instance) {
      QualityValidator.instance = new QualityValidator()
    }
    return QualityValidator.instance
  }

  // Main validation method
  async validateContent(
    content: string,
    title: string,
    metadata: {
      excerpt: string
      tags: string[]
      categories: string[]
      focusKeyword?: string
    },
    settings: AISettings,
    userId: string,
    options: ValidationOptions = {
      strictMode: false,
      targetAudience: 'intermediate',
      contentType: 'guide',
      minWordCount: 800,
      maxWordCount: 5000,
      requireImages: false,
      requireCodeExamples: false
    }
  ): Promise<ValidationResult> {
    try {
      // Run all validation checks
      const [
        readabilityScore,
        seoScore,
        engagementScore,
        structureScore,
        accuracyScore
      ] = await Promise.all([
        this.validateReadability(content, options),
        this.validateSEO(content, title, metadata, options),
        this.validateEngagement(content, title, metadata, settings, userId),
        this.validateStructure(content, options),
        this.validateAccuracy(content, settings, userId)
      ])

      // Calculate overall score
      const overall = Math.round(
        (readabilityScore.score + seoScore.score + engagementScore.score + 
         structureScore.score + accuracyScore.score) / 5
      )

      const score: QualityScore = {
        overall,
        readability: readabilityScore.score,
        seo: seoScore.score,
        engagement: engagementScore.score,
        structure: structureScore.score,
        accuracy: accuracyScore.score
      }

      // Collect all issues
      const issues: QualityIssue[] = [
        ...readabilityScore.issues,
        ...seoScore.issues,
        ...engagementScore.issues,
        ...structureScore.issues,
        ...accuracyScore.issues
      ]

      // Generate recommendations
      const recommendations = await this.generateRecommendations(
        content,
        score,
        issues,
        settings,
        userId
      )

      // Determine if ready to publish
      const isPublishReady = this.determinePublishReadiness(score, issues, options)

      return {
        score,
        issues,
        recommendations,
        isPublishReady,
        estimatedImprovements: {
          readability: readabilityScore.improvements,
          seo: seoScore.improvements,
          engagement: engagementScore.improvements
        }
      }

    } catch (error) {
      console.error('Content validation failed:', error)
      return this.getFallbackValidation(content)
    }
  }

  private async validateReadability(
    content: string,
    options: ValidationOptions
  ): Promise<{ score: number; issues: QualityIssue[]; improvements: string[] }> {
    const issues: QualityIssue[] = []
    const improvements: string[] = []
    let score = 100

    // Word count validation
    const wordCount = this.countWords(content)
    if (wordCount < options.minWordCount) {
      issues.push({
        type: 'warning',
        category: 'readability',
        message: `Content is too short (${wordCount} words)`,
        suggestion: `Add more content to reach at least ${options.minWordCount} words`,
        severity: 'medium'
      })
      score -= 15
      improvements.push('Expand content with more detailed explanations')
    }

    if (wordCount > options.maxWordCount) {
      issues.push({
        type: 'warning',
        category: 'readability',
        message: `Content is too long (${wordCount} words)`,
        suggestion: `Consider breaking into multiple posts or reducing to under ${options.maxWordCount} words`,
        severity: 'low'
      })
      score -= 5
      improvements.push('Break long content into digestible sections')
    }

    // Sentence length analysis
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0)
    const avgSentenceLength = wordCount / sentences.length
    
    if (avgSentenceLength > 25) {
      issues.push({
        type: 'suggestion',
        category: 'readability',
        message: 'Average sentence length is too long',
        suggestion: 'Break long sentences into shorter, more digestible ones',
        severity: 'medium'
      })
      score -= 10
      improvements.push('Use shorter sentences for better readability')
    }

    // Paragraph length analysis
    const paragraphs = content.split('\n\n').filter(p => p.trim().length > 0)
    const longParagraphs = paragraphs.filter(p => this.countWords(p) > 150)
    
    if (longParagraphs.length > paragraphs.length * 0.3) {
      issues.push({
        type: 'suggestion',
        category: 'readability',
        message: 'Many paragraphs are too long',
        suggestion: 'Break long paragraphs into smaller chunks',
        severity: 'low'
      })
      score -= 5
      improvements.push('Use shorter paragraphs with clear topic sentences')
    }

    // Subheading frequency
    const subheadings = (content.match(/^#{2,4}\s+/gm) || []).length
    const expectedSubheadings = Math.floor(wordCount / 300)
    
    if (subheadings < expectedSubheadings * 0.7) {
      issues.push({
        type: 'suggestion',
        category: 'readability',
        message: 'Content needs more subheadings',
        suggestion: 'Add subheadings every 200-300 words to improve scannability',
        severity: 'medium'
      })
      score -= 8
      improvements.push('Add more subheadings to break up content')
    }

    return { score: Math.max(score, 0), issues, improvements }
  }

  private async validateSEO(
    content: string,
    title: string,
    metadata: any,
    options: ValidationOptions
  ): Promise<{ score: number; issues: QualityIssue[]; improvements: string[] }> {
    const issues: QualityIssue[] = []
    const improvements: string[] = []
    let score = 100

    // Title length
    if (title.length < 30 || title.length > 60) {
      issues.push({
        type: 'warning',
        category: 'seo',
        message: 'Title length is not optimal for SEO',
        suggestion: 'Keep title between 30-60 characters for best search results',
        severity: 'medium'
      })
      score -= 15
      improvements.push('Optimize title length for search engines')
    }

    // Focus keyword in title
    if (metadata.focusKeyword && !title.toLowerCase().includes(metadata.focusKeyword.toLowerCase())) {
      issues.push({
        type: 'warning',
        category: 'seo',
        message: 'Focus keyword not found in title',
        suggestion: `Include "${metadata.focusKeyword}" in the title`,
        severity: 'high'
      })
      score -= 20
      improvements.push('Include focus keyword in title naturally')
    }

    // Meta description length
    if (metadata.excerpt.length < 120 || metadata.excerpt.length > 160) {
      issues.push({
        type: 'warning',
        category: 'seo',
        message: 'Meta description length is not optimal',
        suggestion: 'Keep meta description between 120-160 characters',
        severity: 'medium'
      })
      score -= 10
      improvements.push('Optimize meta description length')
    }

    // Keyword density
    if (metadata.focusKeyword) {
      const keywordCount = (content.toLowerCase().match(new RegExp(metadata.focusKeyword.toLowerCase(), 'g')) || []).length
      const density = keywordCount / this.countWords(content)
      
      if (density < 0.005) {
        issues.push({
          type: 'suggestion',
          category: 'seo',
          message: 'Focus keyword appears too rarely',
          suggestion: `Use "${metadata.focusKeyword}" more naturally throughout the content`,
          severity: 'medium'
        })
        score -= 10
        improvements.push('Increase focus keyword usage naturally')
      } else if (density > 0.03) {
        issues.push({
          type: 'warning',
          category: 'seo',
          message: 'Focus keyword appears too frequently',
          suggestion: 'Reduce keyword usage to avoid over-optimization',
          severity: 'medium'
        })
        score -= 15
        improvements.push('Reduce keyword stuffing')
      }
    }

    // Internal links
    const internalLinks = (content.match(/\[([^\]]+)\]\(\/[^)]+\)/g) || []).length
    if (internalLinks < 2) {
      issues.push({
        type: 'suggestion',
        category: 'seo',
        message: 'Content needs more internal links',
        suggestion: 'Add 2-5 relevant internal links to other pages',
        severity: 'low'
      })
      score -= 5
      improvements.push('Add relevant internal links')
    }

    return { score: Math.max(score, 0), issues, improvements }
  }

  private async validateEngagement(
    content: string,
    title: string,
    metadata: any,
    settings: AISettings,
    userId: string
  ): Promise<{ score: number; issues: QualityIssue[]; improvements: string[] }> {
    const engagementPrompt = `
Analyze this blog post for engagement factors:

Title: ${title}
Content: ${content.substring(0, 1500)}...

Rate engagement potential (0-100) based on:
1. Hook strength in introduction
2. Value proposition clarity
3. Actionable content
4. Call-to-action presence
5. Reader benefit focus

Return JSON:
{
  "score": 85,
  "issues": [
    {
      "message": "Issue description",
      "suggestion": "How to fix it",
      "severity": "low|medium|high"
    }
  ],
  "improvements": ["improvement 1", "improvement 2"]
}
`

    const request: AIRequest = {
      provider: settings.provider,
      model: settings.models.content,
      prompt: engagementPrompt,
      maxTokens: 800,
      temperature: 0.3,
      systemPrompt: 'You are a content engagement expert analyzing blog posts for reader appeal.'
    }

    try {
      const response = await optimizedAIManager.generateOptimizedResponse(request, userId, settings)
      const analysis = JSON.parse(response.content)
      
      const issues: QualityIssue[] = analysis.issues.map((issue: any) => ({
        type: 'suggestion' as const,
        category: 'engagement' as const,
        message: issue.message,
        suggestion: issue.suggestion,
        severity: issue.severity
      }))

      return {
        score: analysis.score || 70,
        issues,
        improvements: analysis.improvements || []
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Failed to validate engagement:', error)
      }
      return {
        score: 70,
        issues: [],
        improvements: ['Add more engaging introduction', 'Include clear call-to-action']
      }
    }
  }

  private async validateStructure(
    content: string,
    options: ValidationOptions
  ): Promise<{ score: number; issues: QualityIssue[]; improvements: string[] }> {
    const issues: QualityIssue[] = []
    const improvements: string[] = []
    let score = 100

    // Check for introduction
    const hasIntro = content.toLowerCase().includes('introduction') || 
                    content.split('\n\n')[0].length > 100
    if (!hasIntro) {
      issues.push({
        type: 'warning',
        category: 'structure',
        message: 'Content lacks a clear introduction',
        suggestion: 'Add an engaging introduction that sets context',
        severity: 'medium'
      })
      score -= 15
      improvements.push('Add clear introduction section')
    }

    // Check for conclusion
    const hasConclusion = content.toLowerCase().includes('conclusion') ||
                         content.toLowerCase().includes('summary') ||
                         content.split('\n\n').slice(-1)[0].length > 100
    if (!hasConclusion) {
      issues.push({
        type: 'suggestion',
        category: 'structure',
        message: 'Content lacks a conclusion',
        suggestion: 'Add a conclusion that summarizes key points',
        severity: 'low'
      })
      score -= 10
      improvements.push('Add conclusion with key takeaways')
    }

    // Check for required elements based on content type
    if (options.requireCodeExamples && !content.includes('```')) {
      issues.push({
        type: 'warning',
        category: 'structure',
        message: 'Tutorial content should include code examples',
        suggestion: 'Add relevant code examples to illustrate concepts',
        severity: 'high'
      })
      score -= 20
      improvements.push('Include practical code examples')
    }

    // Check for lists or bullet points
    const hasLists = content.includes('- ') || content.includes('1.') || content.includes('*')
    if (!hasLists && this.countWords(content) > 800) {
      issues.push({
        type: 'suggestion',
        category: 'structure',
        message: 'Long content would benefit from lists',
        suggestion: 'Use bullet points or numbered lists to break up information',
        severity: 'low'
      })
      score -= 5
      improvements.push('Use lists to organize information')
    }

    return { score: Math.max(score, 0), issues, improvements }
  }

  private async validateAccuracy(
    content: string,
    settings: AISettings,
    userId: string
  ): Promise<{ score: number; issues: QualityIssue[]; improvements: string[] }> {
    // Basic accuracy checks (can be expanded with fact-checking APIs)
    const issues: QualityIssue[] = []
    const improvements: string[] = []
    let score = 90

    // Check for broken markdown
    const brokenMarkdown = content.match(/\[([^\]]*)\]\(\s*\)/g)
    if (brokenMarkdown) {
      issues.push({
        type: 'error',
        category: 'accuracy',
        message: 'Found broken links in content',
        suggestion: 'Fix or remove broken markdown links',
        severity: 'high'
      })
      score -= 15
      improvements.push('Fix broken links and formatting')
    }

    // Check for placeholder text
    const placeholders = ['TODO', 'PLACEHOLDER', 'FIXME', 'XXX']
    const hasPlaceholders = placeholders.some(p => content.includes(p))
    if (hasPlaceholders) {
      issues.push({
        type: 'error',
        category: 'accuracy',
        message: 'Content contains placeholder text',
        suggestion: 'Replace all placeholder text with actual content',
        severity: 'high'
      })
      score -= 20
      improvements.push('Complete all placeholder content')
    }

    return { score: Math.max(score, 0), issues, improvements }
  }

  private async generateRecommendations(
    content: string,
    score: QualityScore,
    issues: QualityIssue[],
    settings: AISettings,
    userId: string
  ): Promise<string[]> {
    const highPriorityIssues = issues.filter(issue => issue.severity === 'high')
    
    if (highPriorityIssues.length === 0) {
      return [
        'Content quality is good overall',
        'Consider the suggested improvements for better performance',
        'Review SEO optimization opportunities'
      ]
    }

    return highPriorityIssues.map(issue => issue.suggestion)
  }

  private determinePublishReadiness(
    score: QualityScore,
    issues: QualityIssue[],
    options: ValidationOptions
  ): boolean {
    const threshold = options.strictMode ? 80 : 70
    const hasHighSeverityErrors = issues.some(issue => 
      issue.type === 'error' && issue.severity === 'high'
    )

    return score.overall >= threshold && !hasHighSeverityErrors
  }

  private countWords(text: string): number {
    return text.split(/\s+/).filter(word => word.length > 0).length
  }

  private getFallbackValidation(content: string): ValidationResult {
    const wordCount = this.countWords(content)
    
    return {
      score: {
        overall: 75,
        readability: 80,
        seo: 70,
        engagement: 75,
        structure: 80,
        accuracy: 90
      },
      issues: [],
      recommendations: [
        'Content appears to be of good quality',
        'Consider adding more internal links',
        'Review for engagement opportunities'
      ],
      isPublishReady: wordCount >= 500,
      estimatedImprovements: {
        readability: ['Use shorter sentences', 'Add more subheadings'],
        seo: ['Optimize title length', 'Add internal links'],
        engagement: ['Strengthen introduction', 'Add call-to-action']
      }
    }
  }
}

// Export singleton instance
export const qualityValidator = QualityValidator.getInstance()

// Utility functions
export const validateContentQuality = async (
  content: string,
  title: string,
  metadata: any,
  settings: AISettings,
  userId: string,
  options?: ValidationOptions
): Promise<ValidationResult> => {
  return await qualityValidator.validateContent(content, title, metadata, settings, userId, options)
}

export const getQualityScore = (content: string): number => {
  const wordCount = content.split(/\s+/).length
  const hasStructure = content.includes('##') && content.includes('\n\n')
  const hasLists = content.includes('- ') || content.includes('1.')
  
  let score = 50
  if (wordCount >= 800) score += 20
  if (hasStructure) score += 15
  if (hasLists) score += 10
  if (wordCount >= 1500) score += 5
  
  return Math.min(score, 100)
}
