// Utility functions for managing blog post view counts

export interface ViewData {
  [slug: string]: number
}

/**
 * Get all view counts from localStorage
 */
export const getAllViews = (): ViewData => {
  if (typeof window === 'undefined') return {}
  
  try {
    const storedViews = localStorage.getItem('blog-views')
    return storedViews ? JSON.parse(storedViews) : {}
  } catch (error) {
    console.error('Error reading views from localStorage:', error)
    return {}
  }
}

/**
 * Get view count for a specific post
 */
export const getPostViews = (slug: string): number => {
  const allViews = getAllViews()
  return allViews[slug] || 0
}

/**
 * Set view count for a specific post (admin function)
 */
export const setPostViews = (slug: string, count: number): void => {
  if (typeof window === 'undefined') return
  
  try {
    const allViews = getAllViews()
    allViews[slug] = Math.max(0, count) // Ensure non-negative
    localStorage.setItem('blog-views', JSON.stringify(allViews))
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Error setting views in localStorage:', error)
    }
  }
}

/**
 * Reset all view counts (admin function)
 */
export const resetAllViews = (): void => {
  if (typeof window === 'undefined') return
  
  try {
    localStorage.removeItem('blog-views')
  } catch (error) {
    console.error('Error resetting views in localStorage:', error)
  }
}

/**
 * Export view data as JSON (for backup/migration)
 */
export const exportViewData = (): string => {
  const allViews = getAllViews()
  return JSON.stringify(allViews, null, 2)
}

/**
 * Import view data from JSON (for backup/migration)
 */
export const importViewData = (jsonData: string): boolean => {
  if (typeof window === 'undefined') return false
  
  try {
    const viewData = JSON.parse(jsonData)
    
    // Validate data structure
    if (typeof viewData !== 'object' || viewData === null) {
      throw new Error('Invalid data format')
    }
    
    // Validate all values are numbers
    for (const [slug, count] of Object.entries(viewData)) {
      if (typeof count !== 'number' || count < 0) {
        throw new Error(`Invalid view count for ${slug}: ${count}`)
      }
    }
    
    localStorage.setItem('blog-views', JSON.stringify(viewData))
    return true
  } catch (error) {
    console.error('Error importing view data:', error)
    return false
  }
}

/**
 * Get total views across all posts
 */
export const getTotalViews = (): number => {
  const allViews = getAllViews()
  return Object.values(allViews).reduce((total, count) => total + count, 0)
}

/**
 * Get most viewed posts
 */
export const getMostViewedPosts = (limit: number = 5): Array<{ slug: string; views: number }> => {
  const allViews = getAllViews()
  return Object.entries(allViews)
    .map(([slug, views]) => ({ slug, views }))
    .sort((a, b) => b.views - a.views)
    .slice(0, limit)
}
