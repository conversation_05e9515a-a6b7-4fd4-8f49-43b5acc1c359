// Utility functions for managing cookie consent

export interface CookieConsentData {
  status: 'accepted' | 'declined' | null
  date: string | null
}

/**
 * Get the current cookie consent status
 */
export const getCookieConsent = (): CookieConsentData => {
  if (typeof window === 'undefined') {
    return { status: null, date: null }
  }

  try {
    const status = localStorage.getItem('cookie-consent') as 'accepted' | 'declined' | null
    const date = localStorage.getItem('cookie-consent-date')
    
    return { status, date }
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Error reading cookie consent from localStorage:', error)
    }
    return { status: null, date: null }
  }
}

/**
 * Set cookie consent status
 */
export const setCookieConsent = (status: 'accepted' | 'declined'): void => {
  if (typeof window === 'undefined') return

  try {
    localStorage.setItem('cookie-consent', status)
    localStorage.setItem('cookie-consent-date', new Date().toISOString())
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Error setting cookie consent in localStorage:', error)
    }
  }
}

/**
 * Clear cookie consent (for testing or reset purposes)
 */
export const clearCookieConsent = (): void => {
  if (typeof window === 'undefined') return

  try {
    localStorage.removeItem('cookie-consent')
    localStorage.removeItem('cookie-consent-date')
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Error clearing cookie consent from localStorage:', error)
    }
  }
}

/**
 * Check if analytics should be enabled based on consent
 */
export const shouldEnableAnalytics = (): boolean => {
  const consent = getCookieConsent()
  return consent.status === 'accepted'
}

/**
 * Check if the consent is expired (older than 1 year)
 */
export const isConsentExpired = (): boolean => {
  const consent = getCookieConsent()
  
  if (!consent.date) return true
  
  try {
    const consentDate = new Date(consent.date)
    const oneYearAgo = new Date()
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1)
    
    return consentDate < oneYearAgo
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Error checking consent expiration:', error)
    }
    return true
  }
}

/**
 * Check if cookie consent banner should be shown
 */
export const shouldShowCookieBanner = (): boolean => {
  const consent = getCookieConsent()
  return !consent.status || isConsentExpired()
}

/**
 * Development helper: Reset cookie consent to test the banner
 * Only available in development mode
 */
export const resetCookieConsentForTesting = (): void => {
  if (process.env.NODE_ENV === 'development') {
    clearCookieConsent()
    // Cookie consent reset for testing - refresh page to see banner
  }
}
