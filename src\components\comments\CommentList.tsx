'use client'

import { useState, useEffect, useRef } from 'react'
import { DatabaseComment } from '@/types'
import { sanitizeCommentContent } from '@/lib/sanitize'
import { getCommentsByPostId, updateComment, deleteComment } from '@/lib/firebase-operations'
import { useAuth } from '@/components/providers/AuthProvider'
import CommentAvatarPopup from './CommentAvatarPopup'

interface CommentListProps {
  postId: string
  newComment?: DatabaseComment | null
}

export default function CommentList({ postId, newComment }: CommentListProps) {
  const { user } = useAuth()
  const [comments, setComments] = useState<DatabaseComment[]>([])
  const [loading, setLoading] = useState(true)
  const [editingComment, setEditingComment] = useState<string | null>(null)
  const [editContent, setEditContent] = useState('')
  const [popupOpen, setPopupOpen] = useState<string | null>(null)
  const avatarRefs = useRef<{ [key: string]: React.RefObject<HTMLImageElement> }>({})

  useEffect(() => {
    loadComments()
  }, [postId])

  // Add new comment to the list when it's submitted (even if pending)
  useEffect(() => {
    if (newComment) {
      setComments(prev => [...prev, newComment])
    }
  }, [newComment])

  const loadComments = async () => {
    try {
      const fetchedComments = await getCommentsByPostId(postId, user?.uid)
      setComments(fetchedComments)
    } catch (error) {
      console.error('Error loading comments:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleEditComment = (commentId: string, currentContent: string) => {
    setEditingComment(commentId)
    setEditContent(currentContent)
  }

  const handleSaveEdit = async (commentId: string) => {
    if (!editContent.trim()) return

    try {
      await updateComment(commentId, editContent)
      setEditingComment(null)
      setEditContent('')
      loadComments() // Refresh to show updated comment
    } catch (error) {
      console.error('Error updating comment:', error)
      alert('Failed to update comment')
    }
  }

  const handleCancelEdit = () => {
    setEditingComment(null)
    setEditContent('')
  }

  const handleDeleteComment = async (commentId: string) => {
    if (!confirm('Are you sure you want to delete this comment?')) return

    try {
      await deleteComment(commentId)
      loadComments() // Refresh to remove deleted comment
    } catch (error) {
      console.error('Error deleting comment:', error)
      alert('Failed to delete comment')
    }
  }

  const handleAvatarClick = (commentId: string) => {
    setPopupOpen(popupOpen === commentId ? null : commentId)
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
      return diffInMinutes < 1 ? 'Just now' : `${diffInMinutes}m ago`
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      if (diffInDays < 7) {
        return `${diffInDays}d ago`
      } else {
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        })
      }
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="flex items-start space-x-3">
              <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (comments.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
          <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          No comments yet
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Be the first to share your thoughts!
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {comments.map((comment) => {
        const isEditing = editingComment === comment.id
        const canEdit = user && user.uid === comment.user_id

        // Create ref for this comment's avatar if it doesn't exist
        if (!avatarRefs.current[comment.id]) {
          avatarRefs.current[comment.id] = { current: null }
        }

        return (
          <div key={comment.id} className="relative">
            <div className="flex items-start space-x-3">
              <img
                ref={avatarRefs.current[comment.id]}
                src={comment.user_avatar}
                alt={comment.user_name}
                className={`w-10 h-10 rounded-full flex-shrink-0 ${canEdit ? 'cursor-pointer hover:ring-2 hover:ring-blue-300 transition-all' : ''}`}
                onClick={() => canEdit && handleAvatarClick(comment.id)}
              />
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {comment.user_name}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {formatDate(comment.created_at)}
                  </span>
                  {/* Only show "Pending Review" badge to admin, not to the comment author */}
                  {comment.status === 'pending' && user?.uid !== comment.user_id && user?.uid === 'KNlrg408xubJeEmwFpUbeDQWBgF3' && (
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200">
                      Pending Review
                    </span>
                  )}
                </div>

                {/* Comment content or edit form */}
                {isEditing ? (
                  <div className="mb-3">
                    <textarea
                      value={editContent}
                      onChange={(e) => setEditContent(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none"
                      rows={3}
                    />
                    <div className="flex items-center space-x-2 mt-2">
                      <button
                        onClick={() => handleSaveEdit(comment.id)}
                        className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
                      >
                        Save
                      </button>
                      <button
                        onClick={handleCancelEdit}
                        className="px-3 py-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 text-sm rounded hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed whitespace-pre-wrap">
                    <span dangerouslySetInnerHTML={{ __html: sanitizeCommentContent(comment.content) }} />
                    {comment.edited && (
                      <span className="text-xs text-gray-400 dark:text-gray-500 ml-2 italic">
                        (edited)
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Avatar popup */}
            <CommentAvatarPopup
              commentId={comment.id}
              commentUserId={comment.user_id}
              isOpen={popupOpen === comment.id}
              onClose={() => setPopupOpen(null)}
              onEdit={() => handleEditComment(comment.id, comment.content)}
              onDelete={() => handleDeleteComment(comment.id)}
              avatarRef={avatarRefs.current[comment.id]}
            />
          </div>
        )
      })}
    </div>
  )
}
