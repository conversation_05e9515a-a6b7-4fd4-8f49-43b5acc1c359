import type { Metadata } from 'next'
import { DM_Sans } from 'next/font/google'
import './globals.css'
import { ThemeProvider } from '@/components/providers/ThemeProvider'
import { AuthProvider } from '@/components/providers/AuthProvider'
import LayoutWrapper from '@/components/layout/LayoutWrapper'

import { Analytics } from '@vercel/analytics/next'
import { SpeedInsights } from '@vercel/speed-insights/next'

const dmSans = DM_Sans({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  display: 'swap',
})

export const metadata: Metadata = {
  metadataBase: new URL('http://localhost:3000'),
  title: '<PERSON> - Personal Blog & Portfolio',
  description: 'Personal blog and portfolio of <PERSON> - Web Developer & AI Automation Specialist',
  keywords: '<PERSON>, web development, AI automation, blog, portfolio',
  authors: [{ name: '<PERSON>' }],
  icons: {
    icon: '/favicon.webp',
    shortcut: '/favicon.webp',
    apple: '/favicon.webp',
  },
  openGraph: {
    title: '<PERSON> - Personal Blog & Portfolio',
    description: 'Personal blog and portfolio of <PERSON> - Web Developer & AI Automation Specialist',
    url: 'https://ernestromelo.com',
    siteName: 'Ernst Romelo',
    type: 'website',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </head>
      <body className={dmSans.className}>
        <AuthProvider>
          <ThemeProvider
            defaultTheme="dark"
            storageKey="blog-theme"
          >
            <LayoutWrapper>
              {children}
            </LayoutWrapper>
          </ThemeProvider>
        </AuthProvider>
        {/* Analytics - wrapped in try/catch to prevent errors */}
        {process.env.NODE_ENV === 'production' && (
          <>
            <Analytics />
            <SpeedInsights />
          </>
        )}
      </body>
    </html>
  )
}
