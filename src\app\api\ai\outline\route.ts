// AI Outline Generation API Endpoint
import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { generateBlogOutline, getOrCreateAISettings, validateBlogOutline } from '@/lib/ai'
import { verifyIdToken } from '@/lib/firebase-admin'
import { ResearchResult } from '@/types/ai'

// Rate limiting store
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

const RATE_LIMIT = {
  maxRequests: 20, // More generous for outline generation
  windowMs: 60 * 60 * 1000, // 1 hour
}

function checkRateLimit(userId: string): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now()
  const userLimit = rateLimitStore.get(userId)

  if (!userLimit || now > userLimit.resetTime) {
    const resetTime = now + RATE_LIMIT.windowMs
    rateLimitStore.set(userId, { count: 1, resetTime })
    return { allowed: true, remaining: RATE_LIMIT.maxRequests - 1, resetTime }
  }

  if (userLimit.count >= RATE_LIMIT.maxRequests) {
    return { allowed: false, remaining: 0, resetTime: userLimit.resetTime }
  }

  userLimit.count++
  rateLimitStore.set(userId, userLimit)
  
  return { 
    allowed: true, 
    remaining: RATE_LIMIT.maxRequests - userLimit.count, 
    resetTime: userLimit.resetTime 
  }
}

export async function POST(request: NextRequest) {
  try {
    // Authentication
    const headersList = headers()
    const authorization = headersList.get('authorization')
    
    if (!authorization?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401 }
      )
    }

    const token = authorization.split('Bearer ')[1]
    const decodedToken = await verifyIdToken(token)
    const userId = decodedToken.uid

    // Rate limiting
    const rateLimit = checkRateLimit(userId)
    if (!rateLimit.allowed) {
      return NextResponse.json(
        { 
          error: 'Rate limit exceeded',
          resetTime: rateLimit.resetTime
        },
        { 
          status: 429,
          headers: {
            'X-RateLimit-Limit': RATE_LIMIT.maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': rateLimit.resetTime.toString()
          }
        }
      )
    }

    // Parse and validate request
    const body = await request.json()
    const { keywords, researchResult, options = {} } = body

    // Validate required fields
    if (!keywords || !Array.isArray(keywords) || keywords.length === 0) {
      return NextResponse.json(
        { error: 'Keywords array is required' },
        { status: 400 }
      )
    }

    if (!researchResult || typeof researchResult !== 'object') {
      return NextResponse.json(
        { error: 'Research result is required' },
        { status: 400 }
      )
    }

    // Validate research result structure
    const requiredFields = ['query', 'sources', 'summary', 'keyPoints']
    for (const field of requiredFields) {
      if (!(field in researchResult)) {
        return NextResponse.json(
          { error: `Research result missing required field: ${field}` },
          { status: 400 }
        )
      }
    }

    // Validate options
    const outlineOptions = {
      targetWordCount: Math.min(Math.max(options.targetWordCount || 2000, 500), 10000),
      targetAudience: options.targetAudience || 'intermediate developers',
      contentType: ['tutorial', 'guide', 'analysis', 'opinion', 'news'].includes(options.contentType) 
        ? options.contentType 
        : 'guide',
      includeIntroConclusion: options.includeIntroConclusion !== false,
      maxChapters: Math.min(Math.max(options.maxChapters || 6, 2), 12)
    }

    // Get user settings
    const settings = await getOrCreateAISettings(userId)

    // Generate outline
    const outline = await generateBlogOutline(
      keywords,
      researchResult as ResearchResult,
      userId,
      outlineOptions
    )

    // Validate generated outline
    const validation = validateBlogOutline(outline)
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          error: 'Generated outline failed validation',
          issues: validation.issues
        },
        { status: 422 }
      )
    }

    // Calculate estimated costs for content generation
    const estimatedCost = Math.round(outline.metadata.estimatedWordCount * 0.002 * 100) / 100 // Rough estimate

    return NextResponse.json(
      {
        success: true,
        data: outline,
        metadata: {
          validation,
          estimatedCost,
          estimatedTokens: outline.metadata.estimatedWordCount * 1.3,
          timestamp: new Date().toISOString()
        }
      },
      {
        status: 200,
        headers: {
          'X-RateLimit-Limit': RATE_LIMIT.maxRequests.toString(),
          'X-RateLimit-Remaining': rateLimit.remaining.toString(),
          'X-RateLimit-Reset': rateLimit.resetTime.toString()
        }
      }
    )

  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('AI Outline API Error:', error)
    }

    if (error instanceof Error) {
      if (error.message.includes('Cost limit exceeded')) {
        return NextResponse.json(
          { error: 'Cost limit exceeded', details: error.message },
          { status: 402 }
        )
      }

      if (error.message.includes('Invalid token')) {
        return NextResponse.json(
          { error: 'Invalid authentication token' },
          { status: 401 }
        )
      }

      if (error.message.includes('Outline generation failed')) {
        return NextResponse.json(
          { error: 'Outline generation failed', details: error.message },
          { status: 500 }
        )
      }
    }

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: process.env.NODE_ENV === 'development' ? (error as Error).message : 'An unexpected error occurred'
      },
      { status: 500 }
    )
  }
}

// PUT endpoint for outline refinement
export async function PUT(request: NextRequest) {
  try {
    const headersList = headers()
    const authorization = headersList.get('authorization')
    
    if (!authorization?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401 }
      )
    }

    const token = authorization.split('Bearer ')[1]
    const decodedToken = await verifyIdToken(token)
    const userId = decodedToken.uid

    const body = await request.json()
    const { outline, feedback } = body

    if (!outline || !feedback) {
      return NextResponse.json(
        { error: 'Outline and feedback are required' },
        { status: 400 }
      )
    }

    // Import outline generator for refinement
    const { outlineGenerator } = await import('@/lib/ai')
    const refinedOutline = await outlineGenerator.refineOutline(outline, feedback, userId)

    return NextResponse.json({
      success: true,
      data: refinedOutline,
      metadata: {
        timestamp: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Outline Refinement Error:', error)
    return NextResponse.json(
      { error: 'Failed to refine outline' },
      { status: 500 }
    )
  }
}

// GET endpoint for outline templates/examples
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const contentType = searchParams.get('contentType') || 'guide'
    const audience = searchParams.get('audience') || 'intermediate'

    // Return outline templates based on content type
    const templates = {
      tutorial: {
        structure: ['Introduction', 'Prerequisites', 'Step-by-step Guide', 'Troubleshooting', 'Conclusion'],
        estimatedSections: 5,
        recommendedWordCount: 1500
      },
      guide: {
        structure: ['Introduction', 'Overview', 'Main Content', 'Best Practices', 'Conclusion'],
        estimatedSections: 5,
        recommendedWordCount: 2000
      },
      analysis: {
        structure: ['Introduction', 'Background', 'Analysis', 'Findings', 'Implications', 'Conclusion'],
        estimatedSections: 6,
        recommendedWordCount: 2500
      }
    }

    return NextResponse.json({
      template: templates[contentType as keyof typeof templates] || templates.guide,
      recommendations: {
        audience,
        contentType,
        suggestedElements: ['tables', 'quotes', 'examples']
      }
    })

  } catch (error) {
    console.error('Outline Template Error:', error)
    return NextResponse.json(
      { error: 'Failed to get outline templates' },
      { status: 500 }
    )
  }
}
