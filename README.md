# <PERSON> - Personal Blog & Portfolio

A modern, minimalist personal blog and portfolio website built with Next.js 14, TypeScript, and Tailwind CSS.

## Features

- 🎨 **Modern Design**: Clean, minimalist interface with subtle animations
- 📝 **Markdown Blog**: Easy content management with markdown files
- 🎯 **Project Showcase**: Portfolio grid with detailed project pages
- 📱 **Responsive**: Optimized for all devices
- 🚀 **Performance**: Fast loading with Next.js optimizations
- 📧 **Smart Forms**: Contact forms with location-based features
- 🔍 **SEO Optimized**: Meta tags and structured data
- ♿ **Accessible**: WCAG compliant design

## Tech Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Content**: Markdown with gray-matter
- **Email**: EmailJS integration ready

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone https://github.com/ernestromelo/blog.git
cd blog
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Content Management

### Adding Blog Posts

1. Log in to the dashboard at `/login`
2. Navigate to "Blog Posts" in the dashboard
3. Click "New Post" to create a blog post
4. Use the built-in editor with markdown support
5. Upload featured images directly through the media library
6. Publish when ready

### Adding Projects

1. Log in to the dashboard at `/login`
2. Navigate to "Projects" in the dashboard
3. Click "New Project" to create a project
4. Fill in project details, technologies, and content
5. Upload project images through the media library
6. Publish when ready

### Image Guidelines

- **Avatar**: Automatically uses admin's Google profile picture. Fallback static avatar can be placed in `public/images/avatar.jpg`
- **Blog/Project Images**: Upload through the dashboard media library
- **Featured Images**: Recommended 1200x630px for optimal display

## Customization

### Personal Information

Update the following files with your information:
- `src/app/layout.tsx` - Site metadata
- Avatar automatically uses admin's Google profile picture
- Create your first blog post through the dashboard

### Email Configuration

To enable contact forms:
1. Sign up for EmailJS
2. Update `src/lib/email.ts` with your service configuration
3. Replace console.log statements with actual EmailJS calls

### Styling

- Colors and themes: `tailwind.config.ts`
- Global styles: `src/app/globals.css`
- Component styles: Individual component files

## Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically

### Other Platforms

The site is a static Next.js app and can be deployed to:
- Netlify
- GitHub Pages
- AWS S3 + CloudFront
- Any static hosting service

## Project Structure

```
├── public/
│   ├── favicon.webp    # Site favicon
│   └── images/
│       └── avatar.jpg  # Fallback profile picture (admin's Google photo used when available)
├── src/
│   ├── app/           # Next.js app router pages
│   │   ├── dashboard/ # Content management dashboard
│   │   ├── blog/      # Blog pages
│   │   └── projects/  # Project pages
│   ├── components/    # React components
│   ├── lib/          # Utility functions & Firebase operations
│   └── types/        # TypeScript types
├── firestore.rules    # Firebase Firestore security rules
├── storage.rules      # Firebase Storage security rules
├── package.json
└── README.md
```

## Contributing

This is a personal website, but feel free to:
- Report bugs
- Suggest improvements
- Use as inspiration for your own site

## License

MIT License - feel free to use this code for your own personal website.

## Contact

- Website: [ernestromelo.com](https://ernestromelo.com)
- Email: <EMAIL>
- Projects: <EMAIL>
