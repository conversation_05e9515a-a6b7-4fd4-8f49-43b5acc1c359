// Automatic Metadata Generation System
'use client'

import { AIRequest, AISettings, ResearchResult } from '@/types/ai'
import { optimizedAIManager } from './optimization'
import { getBlogPosts } from '@/lib/firebase-operations'

export interface GeneratedMetadata {
  title: string
  alternativeTitles: string[]
  excerpt: string
  metaDescription: string
  categories: string[]
  tags: string[]
  seoKeywords: string[]
  focusKeyword: string
  readingTime: number
  wordCount: number
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  contentType: 'tutorial' | 'guide' | 'analysis' | 'opinion' | 'news' | 'review'
}

export interface MetadataOptions {
  maxTags: number
  maxCategories: number
  maxSeoKeywords: number
  includeAlternativeTitles: boolean
  optimizeForSeo: boolean
  targetAudience: string
  contentFocus: string[]
}

export interface ContentAnalysis {
  mainTopics: string[]
  keyTerms: string[]
  technicalLevel: 'beginner' | 'intermediate' | 'advanced'
  contentStructure: {
    hasIntroduction: boolean
    hasConclusion: boolean
    hasCodeExamples: boolean
    hasTables: boolean
    hasLists: boolean
  }
  sentiment: 'positive' | 'neutral' | 'negative'
  actionability: 'high' | 'medium' | 'low'
}

// Metadata Generator Class
export class MetadataGenerator {
  private static instance: MetadataGenerator

  static getInstance(): MetadataGenerator {
    if (!MetadataGenerator.instance) {
      MetadataGenerator.instance = new MetadataGenerator()
    }
    return MetadataGenerator.instance
  }

  // Main method to generate comprehensive metadata
  async generateMetadata(
    content: string,
    researchResult: ResearchResult,
    settings: AISettings,
    userId: string,
    options: MetadataOptions = {
      maxTags: 8,
      maxCategories: 3,
      maxSeoKeywords: 10,
      includeAlternativeTitles: true,
      optimizeForSeo: true,
      targetAudience: 'developers',
      contentFocus: []
    }
  ): Promise<GeneratedMetadata> {
    try {
      // Analyze content structure and topics
      const contentAnalysis = await this.analyzeContent(content, settings, userId)
      
      // Generate title and alternatives
      const titleData = await this.generateTitles(
        content,
        researchResult,
        contentAnalysis,
        settings,
        userId,
        options
      )

      // Generate excerpt and meta description
      const excerptData = await this.generateExcerpts(
        content,
        titleData.title,
        settings,
        userId,
        options
      )

      // Generate categories and tags
      const taxonomyData = await this.generateTaxonomy(
        content,
        contentAnalysis,
        researchResult,
        settings,
        userId,
        options
      )

      // Generate SEO keywords
      const seoData = await this.generateSeoKeywords(
        content,
        titleData.title,
        taxonomyData,
        settings,
        userId,
        options
      )

      // Calculate reading metrics
      const readingMetrics = this.calculateReadingMetrics(content)

      return {
        title: titleData.title,
        alternativeTitles: titleData.alternatives,
        excerpt: excerptData.excerpt,
        metaDescription: excerptData.metaDescription,
        categories: taxonomyData.categories,
        tags: taxonomyData.tags,
        seoKeywords: seoData.keywords,
        focusKeyword: seoData.focusKeyword,
        readingTime: readingMetrics.readingTime,
        wordCount: readingMetrics.wordCount,
        difficulty: contentAnalysis.technicalLevel,
        contentType: this.determineContentType(content, contentAnalysis)
      }

    } catch (error) {
      console.error('Metadata generation failed:', error)
      return this.generateFallbackMetadata(content, researchResult)
    }
  }

  private async analyzeContent(
    content: string,
    settings: AISettings,
    userId: string
  ): Promise<ContentAnalysis> {
    const analysisPrompt = `
Analyze this blog post content and provide detailed insights:

Content: ${content.substring(0, 2000)}...

Analyze and return JSON with:
{
  "mainTopics": ["topic1", "topic2", "topic3"],
  "keyTerms": ["term1", "term2", "term3"],
  "technicalLevel": "beginner|intermediate|advanced",
  "contentStructure": {
    "hasIntroduction": true/false,
    "hasConclusion": true/false,
    "hasCodeExamples": true/false,
    "hasTables": true/false,
    "hasLists": true/false
  },
  "sentiment": "positive|neutral|negative",
  "actionability": "high|medium|low"
}

Focus on identifying the main themes, technical complexity, and content structure.
`

    const request: AIRequest = {
      provider: settings.provider,
      model: settings.models.metadata,
      prompt: analysisPrompt,
      maxTokens: 800,
      temperature: 0.2,
      systemPrompt: 'You are a content analyst specializing in blog post analysis and categorization.'
    }

    try {
      const response = await optimizedAIManager.generateOptimizedResponse(request, userId, settings)
      return JSON.parse(response.content)
    } catch (error) {
      console.error('Failed to analyze content:', error)
      return this.getFallbackAnalysis(content)
    }
  }

  private async generateTitles(
    content: string,
    researchResult: ResearchResult,
    analysis: ContentAnalysis,
    settings: AISettings,
    userId: string,
    options: MetadataOptions
  ): Promise<{ title: string; alternatives: string[] }> {
    const titlePrompt = `
Generate compelling blog post titles based on this content:

Content Summary: ${content.substring(0, 1000)}...
Main Topics: ${analysis.mainTopics.join(', ')}
Target Audience: ${options.targetAudience}
Technical Level: ${analysis.technicalLevel}

Research Keywords: ${researchResult.keyPoints.slice(0, 5).join(', ')}

Generate:
1. One primary title (50-60 characters, SEO-optimized)
${options.includeAlternativeTitles ? '2. 3-5 alternative titles with different angles' : ''}

Requirements:
- Include main keyword naturally
- Make it compelling and clickable
- Match the technical level
- Appeal to the target audience
${options.optimizeForSeo ? '- Optimize for search engines' : ''}

Return JSON:
{
  "title": "Primary title",
  "alternatives": ["alt1", "alt2", "alt3"]
}
`

    const request: AIRequest = {
      provider: settings.provider,
      model: settings.models.metadata,
      prompt: titlePrompt,
      maxTokens: 600,
      temperature: 0.7,
      systemPrompt: 'You are an expert copywriter specializing in compelling blog post titles that drive engagement.'
    }

    try {
      const response = await optimizedAIManager.generateOptimizedResponse(request, userId, settings)
      const titleData = JSON.parse(response.content)
      return {
        title: titleData.title || 'Generated Blog Post',
        alternatives: titleData.alternatives || []
      }
    } catch (error) {
      console.error('Failed to generate titles:', error)
      return {
        title: 'Complete Guide to ' + analysis.mainTopics[0] || 'Your Topic',
        alternatives: []
      }
    }
  }

  private async generateExcerpts(
    content: string,
    title: string,
    settings: AISettings,
    userId: string,
    options: MetadataOptions
  ): Promise<{ excerpt: string; metaDescription: string }> {
    const excerptPrompt = `
Create compelling excerpt and meta description for this blog post:

Title: ${title}
Content: ${content.substring(0, 1500)}...

Generate:
1. Blog excerpt (150-200 words) - engaging summary for blog listings
2. Meta description (150-160 characters) - SEO-optimized for search results

Requirements:
- Highlight key benefits for readers
- Include main keywords naturally
- Make it compelling and clickable
- Focus on value proposition

Return JSON:
{
  "excerpt": "Blog excerpt text...",
  "metaDescription": "Meta description text..."
}
`

    const request: AIRequest = {
      provider: settings.provider,
      model: settings.models.metadata,
      prompt: excerptPrompt,
      maxTokens: 500,
      temperature: 0.6,
      systemPrompt: 'You are an expert at writing compelling blog excerpts and meta descriptions that drive clicks.'
    }

    try {
      const response = await optimizedAIManager.generateOptimizedResponse(request, userId, settings)
      const excerptData = JSON.parse(response.content)
      return {
        excerpt: excerptData.excerpt || 'An informative blog post about the topic.',
        metaDescription: excerptData.metaDescription || title.substring(0, 160)
      }
    } catch (error) {
      console.error('Failed to generate excerpts:', error)
      return {
        excerpt: 'An informative blog post covering key concepts and practical insights.',
        metaDescription: title.substring(0, 160)
      }
    }
  }

  private async generateTaxonomy(
    content: string,
    analysis: ContentAnalysis,
    researchResult: ResearchResult,
    settings: AISettings,
    userId: string,
    options: MetadataOptions
  ): Promise<{ categories: string[]; tags: string[] }> {
    // Get existing categories and tags for consistency
    const existingTaxonomy = await this.getExistingTaxonomy()

    const taxonomyPrompt = `
Generate categories and tags for this blog post:

Content Topics: ${analysis.mainTopics.join(', ')}
Key Terms: ${analysis.keyTerms.join(', ')}
Research Keywords: ${researchResult.keyPoints.slice(0, 5).join(', ')}

Existing Categories: ${existingTaxonomy.categories.join(', ')}
Existing Tags: ${existingTaxonomy.tags.slice(0, 20).join(', ')}

Generate:
- ${options.maxCategories} broad categories (use existing when appropriate)
- ${options.maxTags} specific tags (mix of existing and new)

Requirements:
- Categories should be broad topic areas
- Tags should be specific keywords/concepts
- Prefer existing taxonomy for consistency
- Ensure relevance to content

Return JSON:
{
  "categories": ["category1", "category2"],
  "tags": ["tag1", "tag2", "tag3", "tag4"]
}
`

    const request: AIRequest = {
      provider: settings.provider,
      model: settings.models.metadata,
      prompt: taxonomyPrompt,
      maxTokens: 400,
      temperature: 0.3,
      systemPrompt: 'You are a content taxonomist organizing blog content with consistent categorization.'
    }

    try {
      const response = await optimizedAIManager.generateOptimizedResponse(request, userId, settings)
      const taxonomyData = JSON.parse(response.content)
      return {
        categories: taxonomyData.categories?.slice(0, options.maxCategories) || ['General'],
        tags: taxonomyData.tags?.slice(0, options.maxTags) || analysis.mainTopics.slice(0, options.maxTags)
      }
    } catch (error) {
      console.error('Failed to generate taxonomy:', error)
      return {
        categories: ['Technology'],
        tags: analysis.mainTopics.slice(0, options.maxTags)
      }
    }
  }

  private async generateSeoKeywords(
    content: string,
    title: string,
    taxonomy: { categories: string[]; tags: string[] },
    settings: AISettings,
    userId: string,
    options: MetadataOptions
  ): Promise<{ keywords: string[]; focusKeyword: string }> {
    const seoPrompt = `
Generate SEO keywords for this blog post:

Title: ${title}
Categories: ${taxonomy.categories.join(', ')}
Tags: ${taxonomy.tags.join(', ')}
Content: ${content.substring(0, 1000)}...

Generate:
1. One primary focus keyword (2-4 words)
2. ${options.maxSeoKeywords} related SEO keywords

Requirements:
- Focus on search intent and user queries
- Include long-tail keywords
- Consider search volume and competition
- Ensure relevance to content
- Mix of broad and specific terms

Return JSON:
{
  "focusKeyword": "primary keyword phrase",
  "keywords": ["keyword1", "keyword2", "keyword3"]
}
`

    const request: AIRequest = {
      provider: settings.provider,
      model: settings.models.metadata,
      prompt: seoPrompt,
      maxTokens: 400,
      temperature: 0.4,
      systemPrompt: 'You are an SEO expert identifying high-value keywords for blog content optimization.'
    }

    try {
      const response = await optimizedAIManager.generateOptimizedResponse(request, userId, settings)
      const seoData = JSON.parse(response.content)
      return {
        focusKeyword: seoData.focusKeyword || taxonomy.tags[0] || 'blog topic',
        keywords: seoData.keywords?.slice(0, options.maxSeoKeywords) || taxonomy.tags.slice(0, options.maxSeoKeywords)
      }
    } catch (error) {
      console.error('Failed to generate SEO keywords:', error)
      return {
        focusKeyword: taxonomy.tags[0] || 'blog topic',
        keywords: taxonomy.tags.slice(0, options.maxSeoKeywords)
      }
    }
  }

  private calculateReadingMetrics(content: string): { readingTime: number; wordCount: number } {
    // Remove markdown formatting for accurate word count
    const plainText = content
      .replace(/#{1,6}\s+/g, '') // Remove headers
      .replace(/\*\*([^*]+)\*\*/g, '$1') // Remove bold
      .replace(/\*([^*]+)\*/g, '$1') // Remove italic
      .replace(/`([^`]+)`/g, '$1') // Remove inline code
      .replace(/```[\s\S]*?```/g, '') // Remove code blocks
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove links
      .replace(/!\[([^\]]*)\]\([^)]+\)/g, '') // Remove images
      .replace(/\|[^|\n]*\|/g, '') // Remove tables
      .replace(/>\s+/g, '') // Remove quotes

    const words = plainText.split(/\s+/).filter(word => word.length > 0)
    const wordCount = words.length
    const readingTime = Math.ceil(wordCount / 200) // 200 words per minute

    return { readingTime, wordCount }
  }

  private determineContentType(content: string, analysis: ContentAnalysis): GeneratedMetadata['contentType'] {
    const contentLower = content.toLowerCase()
    
    if (contentLower.includes('tutorial') || contentLower.includes('step by step') || analysis.contentStructure.hasCodeExamples) {
      return 'tutorial'
    }
    if (contentLower.includes('guide') || contentLower.includes('how to')) {
      return 'guide'
    }
    if (contentLower.includes('analysis') || contentLower.includes('comparison')) {
      return 'analysis'
    }
    if (contentLower.includes('opinion') || contentLower.includes('think') || analysis.sentiment !== 'neutral') {
      return 'opinion'
    }
    if (contentLower.includes('review') || contentLower.includes('rating')) {
      return 'review'
    }
    
    return 'guide' // Default
  }

  private async getExistingTaxonomy(): Promise<{ categories: string[]; tags: string[] }> {
    try {
      const posts = await getBlogPosts()
      const categories = new Set<string>()
      const tags = new Set<string>()

      posts.forEach(post => {
        post.categories?.forEach(cat => categories.add(cat))
        post.tags?.forEach(tag => tags.add(tag))
      })

      return {
        categories: Array.from(categories),
        tags: Array.from(tags)
      }
    } catch (error) {
      console.error('Failed to get existing taxonomy:', error)
      return {
        categories: ['Technology', 'Development', 'Tutorial'],
        tags: ['javascript', 'react', 'nodejs', 'web development']
      }
    }
  }

  private getFallbackAnalysis(content: string): ContentAnalysis {
    return {
      mainTopics: ['Technology', 'Development'],
      keyTerms: ['programming', 'development', 'tutorial'],
      technicalLevel: 'intermediate',
      contentStructure: {
        hasIntroduction: content.includes('introduction') || content.includes('intro'),
        hasConclusion: content.includes('conclusion') || content.includes('summary'),
        hasCodeExamples: content.includes('```') || content.includes('`'),
        hasTables: content.includes('|'),
        hasLists: content.includes('- ') || content.includes('1.')
      },
      sentiment: 'neutral',
      actionability: 'medium'
    }
  }

  private generateFallbackMetadata(content: string, researchResult: ResearchResult): GeneratedMetadata {
    const readingMetrics = this.calculateReadingMetrics(content)
    
    return {
      title: 'Complete Guide to ' + (researchResult.keyPoints[0] || 'Your Topic'),
      alternativeTitles: [],
      excerpt: 'A comprehensive guide covering key concepts and practical insights.',
      metaDescription: 'Learn about key concepts and practical applications in this comprehensive guide.',
      categories: ['Technology'],
      tags: researchResult.keyPoints.slice(0, 5),
      seoKeywords: researchResult.keyPoints.slice(0, 8),
      focusKeyword: researchResult.keyPoints[0] || 'tutorial',
      readingTime: readingMetrics.readingTime,
      wordCount: readingMetrics.wordCount,
      difficulty: 'intermediate',
      contentType: 'guide'
    }
  }
}

// Export singleton instance
export const metadataGenerator = MetadataGenerator.getInstance()

// Utility functions
export const generateContentMetadata = async (
  content: string,
  researchResult: ResearchResult,
  settings: AISettings,
  userId: string,
  options?: MetadataOptions
): Promise<GeneratedMetadata> => {
  return await metadataGenerator.generateMetadata(content, researchResult, settings, userId, options)
}

export const calculateReadingTime = (content: string): number => {
  return metadataGenerator['calculateReadingMetrics'](content).readingTime
}

export const analyzeContentDifficulty = (content: string): 'beginner' | 'intermediate' | 'advanced' => {
  const technicalTerms = ['API', 'algorithm', 'framework', 'architecture', 'implementation', 'optimization']
  const advancedTerms = ['microservices', 'kubernetes', 'machine learning', 'blockchain', 'distributed systems']
  
  const contentLower = content.toLowerCase()
  const technicalCount = technicalTerms.filter(term => contentLower.includes(term.toLowerCase())).length
  const advancedCount = advancedTerms.filter(term => contentLower.includes(term.toLowerCase())).length
  
  if (advancedCount > 2 || technicalCount > 5) return 'advanced'
  if (technicalCount > 2) return 'intermediate'
  return 'beginner'
}
