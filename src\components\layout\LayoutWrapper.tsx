'use client'

import { usePathname } from 'next/navigation'
import Header from '@/components/Header'
import Footer from '@/components/Footer'
import ContactModal from '@/components/ContactModal'
import LeftSidebar from '@/components/LeftSidebar'
import CookieConsent from '@/components/CookieConsent'

interface LayoutWrapperProps {
  children: React.ReactNode
}

export default function LayoutWrapper({ children }: LayoutWrapperProps) {
  const pathname = usePathname()
  const isDashboard = pathname?.startsWith('/dashboard')

  if (isDashboard) {
    // Dashboard pages get minimal layout
    return <>{children}</>
  }

  // Regular pages get full layout
  return (
    <div className="min-h-screen relative flex flex-col bg-gradient-to-br from-blue-50/80 via-white to-orange-50/60 dark:from-gray-900/80 dark:via-gray-800 dark:to-gray-900/60">
      {/* Subtle Background Pattern */}
      <div className="fixed inset-0 bg-grid-pattern-light dark:bg-grid-pattern-dark opacity-30 pointer-events-none z-0"></div>
      <div className="fixed inset-0 bg-gradient-to-r from-transparent via-blue-50/20 dark:via-gray-800/20 to-transparent pointer-events-none z-0"></div>

      {/* Left Sidebar */}
      <LeftSidebar />

      <Header />
      <main className="pt-20 relative z-10 flex-grow">
        {children}
      </main>
      <Footer />
      <ContactModal />
      <CookieConsent />
    </div>
  )
}
