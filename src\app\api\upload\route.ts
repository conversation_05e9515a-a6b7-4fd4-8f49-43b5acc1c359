import { NextRequest, NextResponse } from 'next/server'
import { withSecurity, SecurityConfigs } from '@/lib/api-security'

// POST /api/upload - Protected endpoint for file uploads
async function handlePost(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only images are allowed.' },
        { status: 400 }
      )
    }

    // Validate file size (5MB limit)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File too large. Maximum size is 5MB.' },
        { status: 400 }
      )
    }

    // Validate file name
    const fileName = file.name
    const dangerousExtensions = ['.exe', '.php', '.js', '.html', '.bat', '.cmd', '.scr']
    const hasDoubleExtension = fileName.split('.').length > 2
    const hasDangerousExtension = dangerousExtensions.some(ext => 
      fileName.toLowerCase().includes(ext)
    )

    if (hasDoubleExtension || hasDangerousExtension) {
      return NextResponse.json(
        { error: 'Invalid file name or extension' },
        { status: 400 }
      )
    }

    // In a real implementation, you would:
    // 1. Upload to Firebase Storage
    // 2. Scan file for malware
    // 3. Generate thumbnail
    // 4. Save metadata to database

    // Mock successful upload
    const mockUrl = `https://firebasestorage.googleapis.com/v0/b/your-project/o/uploads%2F${Date.now()}-${fileName}?alt=media`

    return NextResponse.json({
      success: true,
      url: mockUrl,
      fileName: fileName,
      size: file.size,
      type: file.type,
      message: 'File uploaded successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Error uploading file:', error)
    return NextResponse.json(
      { error: 'Failed to upload file' },
      { status: 500 }
    )
  }
}

// Apply security middleware - requires authentication
export const POST = withSecurity(handlePost, {
  ...SecurityConfigs.protected,
  rateLimit: {
    maxRequests: 20, // Stricter limit for file uploads
    windowMs: 60 * 60 * 1000 // 1 hour
  }
})
