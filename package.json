{"name": "ernest-romelo-blog", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "analyze": "set ANALYZE=true && npm run build"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@google/generative-ai": "^0.24.1", "@heroicons/react": "^2.2.0", "@mailchimp/mailchimp_marketing": "^3.0.80", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@types/dompurify": "^3.0.5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "autoprefixer": "^10.0.1", "cheerio": "^1.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dompurify": "^3.2.6", "eslint": "^8", "eslint-config-next": "14.0.4", "firebase": "^11.9.1", "firebase-admin": "^13.4.0", "framer-motion": "^10.18.0", "js-cookie": "^3.0.5", "lucide-react": "^0.513.0", "next": "14.0.4", "openai": "^5.6.0", "postcss": "^8", "puppeteer": "^24.10.2", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.48.2", "react-markdown": "^9.0.1", "recharts": "^2.15.4", "rehype-highlight": "^7.0.0", "rehype-raw": "^7.0.0", "remark": "^15.0.1", "remark-gfm": "^4.0.0", "remark-html": "^16.0.1", "sitemap-parser": "^0.1.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.3.0", "tailwindcss-animate": "^1.0.7", "tiktoken": "^1.0.21", "typescript": "^5", "uuid": "^9.0.1", "xml2js": "^0.6.2"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.4", "@types/cheerio": "^0.22.35", "@types/js-cookie": "^3.0.6", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^9.0.7", "@types/xml2js": "^0.4.14"}}