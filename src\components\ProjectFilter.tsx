'use client'

import { useState, useEffect } from 'react'
import { Project } from '@/types'

interface ProjectFilterProps {
  projects: Project[]
  onFilterChange: (filteredProjects: Project[]) => void
}

export default function ProjectFilter({ projects, onFilterChange }: ProjectFilterProps) {
  const [activeFilter, setActiveFilter] = useState<string>('All')
  
  // Extract unique industries from projects
  const allIndustries = Array.from(
    new Set(
      projects
        .map(project => project.industry)
        .filter(industry => industry && industry.trim() !== '')
    )
  ).sort()

  // Use only industries for filter options
  const filterOptions = [
    'All',
    ...allIndustries
  ]

  // Get project count for each filter
  const getProjectCount = (filter: string) => {
    if (filter === 'All') return projects.length
    return projects.filter(project =>
      project.industry && project.industry === filter
    ).length
  }

  // Filter projects based on active filter
  useEffect(() => {
    if (activeFilter === 'All') {
      onFilterChange(projects)
    } else {
      const filtered = projects.filter(project =>
        project.industry && project.industry === activeFilter
      )
      onFilterChange(filtered)
    }
  }, [activeFilter, projects, onFilterChange])

  const handleFilterClick = (filter: string) => {
    setActiveFilter(filter)
  }

  return (
    <div className="mb-8">
      <div className="flex items-center flex-wrap">
        <span className="text-gray-700 dark:text-gray-300 font-medium mr-2">Filter by</span>
        {filterOptions.map((filter, index) => {
          const count = getProjectCount(filter)
          const isActive = activeFilter === filter

          return (
            <span key={filter} className="inline-flex items-center">
              <button
                onClick={() => handleFilterClick(filter)}
                className={`
                  px-0 py-1 text-sm font-bold transition-colors duration-200 border-none bg-transparent
                  ${isActive
                    ? 'text-red-500 dark:text-red-400'
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  }
                `}
              >
                {filter}
                <sup className={`ml-1 text-xs ${isActive ? 'text-red-400 dark:text-red-300' : 'text-gray-400 dark:text-gray-500'}`}>
                  {String(count).padStart(2, '0')}
                </sup>
              </button>
              {index < filterOptions.length - 1 && (
                <span className="mx-2 text-gray-400 dark:text-gray-500">/</span>
              )}
            </span>
          )
        })}
      </div>
    </div>
  )
}
