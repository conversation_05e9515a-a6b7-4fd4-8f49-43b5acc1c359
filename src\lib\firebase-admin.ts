import { initializeApp, getApps, cert } from 'firebase-admin/app'
import { getAuth } from 'firebase-admin/auth'

// Initialize Firebase Admin SDK
if (!getApps().length) {
  try {
    const projectId = process.env.FIREBASE_PROJECT_ID || process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID
    const clientEmail = process.env.FIREBASE_CLIENT_EMAIL
    const privateKey = process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n')

    if (projectId && clientEmail && privateKey) {
      initializeApp({
        credential: cert({
          projectId,
          clientEmail,
          privateKey,
        }),
      })
    } else {
      // Initialize with just project ID for basic functionality
      if (projectId) {
        initializeApp({
          projectId,
        })
      }
    }
  } catch (error) {
    // Firebase admin initialization failed
  }
}

let auth: any = null

try {
  auth = getAuth()
} catch (error) {
  // Firebase Admin Auth not available
}

export async function verifyIdToken(idToken: string) {
  if (!auth) {
    throw new Error('Firebase Admin Auth not initialized')
  }
  
  try {
    const decodedToken = await auth.verifyIdToken(idToken)
    return decodedToken
  } catch (error) {
    throw new Error('Invalid token')
  }
}

export { auth }
