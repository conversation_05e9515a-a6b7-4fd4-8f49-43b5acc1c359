'use client'

import { useEffect, useState } from 'react'

// CSRF token management
class CSRFManager {
  private static instance: CSRFManager
  private token: string | null = null
  private readonly tokenKey = 'csrf_token'

  private constructor() {
    this.initializeToken()
  }

  public static getInstance(): CSRFManager {
    if (!CSRFManager.instance) {
      CSRFManager.instance = new CSRFManager()
    }
    return CSRFManager.instance
  }

  private generateToken(): string {
    // Generate a cryptographically secure random token
    const array = new Uint8Array(32)
    if (typeof window !== 'undefined' && window.crypto) {
      window.crypto.getRandomValues(array)
    } else {
      // Fallback for environments without crypto API
      for (let i = 0; i < array.length; i++) {
        array[i] = Math.floor(Math.random() * 256)
      }
    }
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
  }

  private initializeToken(): void {
    if (typeof window === 'undefined') return

    // Check if token exists in sessionStorage
    const existingToken = sessionStorage.getItem(this.tokenKey)
    if (existingToken) {
      this.token = existingToken
    } else {
      // Generate new token
      this.token = this.generateToken()
      sessionStorage.setItem(this.tokenKey, this.token)
    }
  }

  public getToken(): string | null {
    return this.token
  }

  public refreshToken(): string {
    this.token = this.generateToken()
    if (typeof window !== 'undefined') {
      sessionStorage.setItem(this.tokenKey, this.token)
    }
    return this.token
  }

  public validateToken(token: string): boolean {
    return this.token === token && token.length === 64
  }

  public clearToken(): void {
    this.token = null
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem(this.tokenKey)
    }
  }
}

// React hook for CSRF token
export function useCSRFToken() {
  const [token, setToken] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const manager = CSRFManager.getInstance()
    const currentToken = manager.getToken()
    setToken(currentToken)
    setIsLoading(false)
  }, [])

  const refreshToken = () => {
    const manager = CSRFManager.getInstance()
    const newToken = manager.refreshToken()
    setToken(newToken)
    return newToken
  }

  return {
    token,
    isLoading,
    refreshToken
  }
}

// CSRF token input component
export function CSRFTokenInput() {
  const { token, isLoading } = useCSRFToken()

  if (isLoading || !token) {
    return null
  }

  return (
    <input
      type="hidden"
      name="csrf_token"
      value={token}
    />
  )
}

// Utility function to add CSRF token to fetch requests
export function addCSRFToken(options: RequestInit = {}): RequestInit {
  const manager = CSRFManager.getInstance()
  const token = manager.getToken()

  if (!token) {
    console.warn('CSRF token not available')
    return options
  }

  const headers = new Headers(options.headers)
  headers.set('X-CSRF-Token', token)

  return {
    ...options,
    headers
  }
}

// Middleware function to validate CSRF tokens on the server
export function validateCSRFToken(request: Request): boolean {
  const token = request.headers.get('X-CSRF-Token')
  
  if (!token) {
    return false
  }

  // In a real implementation, you would:
  // 1. Store tokens server-side with expiration
  // 2. Validate against stored tokens
  // 3. Check token format and entropy
  
  // For now, just validate format
  return token.length === 64 && /^[a-f0-9]+$/.test(token)
}

// Enhanced fetch function with CSRF protection
export async function secureRequest(url: string, options: RequestInit = {}) {
  const secureOptions = addCSRFToken(options)
  
  try {
    const response = await fetch(url, secureOptions)
    
    // Handle CSRF token errors
    if (response.status === 403) {
      const errorData = await response.json().catch(() => ({}))
      if (errorData.error?.includes('CSRF')) {
        // Refresh token and retry
        const manager = CSRFManager.getInstance()
        manager.refreshToken()
        const retryOptions = addCSRFToken(options)
        return fetch(url, retryOptions)
      }
    }
    
    return response
  } catch (error) {
    console.error('Secure request failed:', error)
    throw error
  }
}

// Form submission helper with CSRF protection
export function createSecureFormSubmit(onSubmit: (data: FormData) => Promise<void>) {
  return async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    
    const form = event.currentTarget
    const formData = new FormData(form)
    
    // Add CSRF token if not already present
    const manager = CSRFManager.getInstance()
    const token = manager.getToken()
    
    if (token && !formData.has('csrf_token')) {
      formData.append('csrf_token', token)
    }
    
    try {
      await onSubmit(formData)
    } catch (error) {
      console.error('Form submission failed:', error)
      throw error
    }
  }
}

// Export the manager instance for advanced usage
export const csrfManager = CSRFManager.getInstance()

// Default export
export default {
  useCSRFToken,
  CSRFTokenInput,
  addCSRFToken,
  validateCSRFToken,
  secureRequest,
  createSecureFormSubmit,
  csrfManager
}
