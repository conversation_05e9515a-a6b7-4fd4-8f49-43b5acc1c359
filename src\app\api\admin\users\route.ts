import { NextRequest, NextResponse } from 'next/server'
import { withSecurity, SecurityConfigs } from '@/lib/api-security'

// GET /api/admin/users - Admin-only endpoint to get user information
async function handleGet(request: NextRequest) {
  try {
    // This is a mock implementation
    // In a real app, you'd fetch user data from your database
    const users = [
      {
        id: 'KNlrg408xubJeEmwFpUbeDQWBgF3',
        email: '<EMAIL>',
        displayName: 'Ernst',
        role: 'admin',
        createdAt: new Date().toISOString(),
        lastLogin: new Date().toISOString()
      }
    ]

    return NextResponse.json({
      success: true,
      users,
      total: users.length
    })
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    )
  }
}

// Apply security middleware - requires admin access
export const GET = withSecurity(handleGet, SecurityConfigs.admin)
