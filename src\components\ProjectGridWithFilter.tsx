'use client'

import { useState } from 'react'
import ProjectGrid from './ProjectGrid'
import ProjectFilter from './ProjectFilter'
import { Project } from '@/types'

interface ProjectGridWithFilterProps {
  projects: Project[]
}

export default function ProjectGridWithFilter({ projects }: ProjectGridWithFilterProps) {
  const [filteredProjects, setFilteredProjects] = useState<Project[]>(projects)

  const handleFilterChange = (filtered: Project[]) => {
    setFilteredProjects(filtered)
  }

  return (
    <>
      {/* Filter */}
      <ProjectFilter projects={projects} onFilterChange={handleFilterChange} />

      {/* Projects Grid */}
      <ProjectGrid projects={filteredProjects} />
    </>
  )
}
