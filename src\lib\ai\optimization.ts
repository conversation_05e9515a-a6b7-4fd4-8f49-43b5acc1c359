// Token Optimization System
'use client'

// Note: t<PERSON><PERSON><PERSON> removed for client-side compatibility
// Using approximation instead
import { 
  AIProvider, 
  AIRequest, 
  AIResponse, 
  TokenUsage, 
  CacheEntry,
  AISettings 
} from '@/types/ai'
import { 
  getCacheEntry, 
  saveCacheEntry, 
  createCache<PERSON>ey, 
  saveTokenUsage,
  getTotalCostForUser 
} from './settings'
import { aiProviderManager } from './providers'

// Token counting utilities
export class TokenOptimizer {
  private static instance: TokenOptimizer
  private tokenCountCache = new Map<string, number>()

  static getInstance(): TokenOptimizer {
    if (!TokenOptimizer.instance) {
      TokenOptimizer.instance = new TokenOptimizer()
    }
    return TokenOptimizer.instance
  }

  // Estimate token count for text
  estimateTokenCount(text: string, model: string = 'gpt-3.5-turbo'): number {
    const cacheKey = `${model}:${text.substring(0, 100)}`
    
    if (this.tokenCountCache.has(cacheKey)) {
      return this.tokenCountCache.get(cacheKey)!
    }

    let tokenCount: number

    // Use approximation for all models (client-side compatible)
    // 1 token ≈ 4 characters for most models
    // This is less accurate but works in browser environment
    tokenCount = Math.ceil(text.length / 4)

    this.tokenCountCache.set(cacheKey, tokenCount)
    return tokenCount
  }

  // Optimize prompt to reduce tokens
  optimizePrompt(prompt: string, maxTokens: number): string {
    const currentTokens = this.estimateTokenCount(prompt)
    
    if (currentTokens <= maxTokens) {
      return prompt
    }

    // Simple optimization: truncate while preserving structure
    const lines = prompt.split('\n')
    let optimizedPrompt = ''
    let tokenCount = 0

    for (const line of lines) {
      const lineTokens = this.estimateTokenCount(line)
      if (tokenCount + lineTokens <= maxTokens - 100) { // Leave buffer
        optimizedPrompt += line + '\n'
        tokenCount += lineTokens
      } else {
        break
      }
    }

    return optimizedPrompt.trim()
  }

  // Split large content into chunks
  splitIntoChunks(content: string, maxTokensPerChunk: number): string[] {
    const chunks: string[] = []
    const paragraphs = content.split('\n\n')
    
    let currentChunk = ''
    let currentTokens = 0

    for (const paragraph of paragraphs) {
      const paragraphTokens = this.estimateTokenCount(paragraph)
      
      if (currentTokens + paragraphTokens <= maxTokensPerChunk) {
        currentChunk += paragraph + '\n\n'
        currentTokens += paragraphTokens
      } else {
        if (currentChunk) {
          chunks.push(currentChunk.trim())
        }
        currentChunk = paragraph + '\n\n'
        currentTokens = paragraphTokens
      }
    }

    if (currentChunk) {
      chunks.push(currentChunk.trim())
    }

    return chunks
  }
}

// Enhanced AI request with optimization
export class OptimizedAIManager {
  private tokenOptimizer: TokenOptimizer
  private requestQueue: Map<string, Promise<AIResponse>> = new Map()

  constructor() {
    this.tokenOptimizer = TokenOptimizer.getInstance()
  }

  async generateOptimizedResponse(
    request: AIRequest,
    userId: string,
    settings: AISettings
  ): Promise<AIResponse> {
    // Check cost limits
    await this.checkCostLimits(userId, settings)

    // Check cache first if enabled
    if (settings.preferences.enableCaching) {
      const cachedResponse = await this.getCachedResponse(request)
      if (cachedResponse) {
        return cachedResponse
      }
    }

    // Optimize prompt
    const optimizedRequest = this.optimizeRequest(request, settings)

    // Check for duplicate requests
    const requestKey = this.createRequestKey(optimizedRequest)
    if (this.requestQueue.has(requestKey)) {
      return await this.requestQueue.get(requestKey)!
    }

    // Create and queue the request
    const responsePromise = this.executeOptimizedRequest(optimizedRequest, userId, settings)
    this.requestQueue.set(requestKey, responsePromise)

    try {
      const response = await responsePromise
      
      // Cache the response if enabled
      if (settings.preferences.enableCaching) {
        await this.cacheResponse(optimizedRequest, response)
      }

      // Track token usage
      await this.trackTokenUsage(optimizedRequest, response, userId)

      return response
    } finally {
      this.requestQueue.delete(requestKey)
    }
  }

  private async checkCostLimits(userId: string, settings: AISettings): Promise<void> {
    const totalCost = await getTotalCostForUser(userId, 30) // Last 30 days
    
    if (totalCost >= settings.preferences.costLimit) {
      throw new Error(
        `Cost limit exceeded. Current usage: $${totalCost.toFixed(2)}, Limit: $${settings.preferences.costLimit}`
      )
    }
  }

  private async getCachedResponse(request: AIRequest): Promise<AIResponse | null> {
    try {
      const cacheKey = createCacheKey(request.provider, request.model, request.prompt)
      const cached = await getCacheEntry(cacheKey)
      
      if (cached) {
        return {
          content: cached.content,
          tokensUsed: cached.tokensUsed,
          cost: 0, // Cached responses are free
          provider: cached.provider,
          model: cached.model,
          cached: true
        }
      }
    } catch (error) {
      console.warn('Cache lookup failed:', error)
    }
    
    return null
  }

  private optimizeRequest(request: AIRequest, settings: AISettings): AIRequest {
    const maxTokens = Math.min(
      request.maxTokens || settings.preferences.maxTokensPerRequest,
      settings.preferences.maxTokensPerRequest
    )

    // Optimize prompt length
    const optimizedPrompt = this.tokenOptimizer.optimizePrompt(
      request.prompt,
      Math.floor(maxTokens * 0.7) // Leave room for response
    )

    return {
      ...request,
      prompt: optimizedPrompt,
      maxTokens,
      temperature: request.temperature ?? settings.preferences.temperature
    }
  }

  private createRequestKey(request: AIRequest): string {
    return `${request.provider}:${request.model}:${request.prompt.substring(0, 100)}`
  }

  private async executeOptimizedRequest(
    request: AIRequest,
    userId: string,
    settings: AISettings
  ): Promise<AIResponse> {
    return await aiProviderManager.generateResponse(request)
  }

  private async cacheResponse(request: AIRequest, response: AIResponse): Promise<void> {
    try {
      const cacheKey = createCacheKey(request.provider, request.model, request.prompt)
      await saveCacheEntry({
        key: cacheKey,
        content: response.content,
        provider: response.provider,
        model: response.model,
        tokensUsed: response.tokensUsed
      })
    } catch (error) {
      console.warn('Failed to cache response:', error)
    }
  }

  private async trackTokenUsage(
    request: AIRequest,
    response: AIResponse,
    userId: string
  ): Promise<void> {
    try {
      const promptTokens = this.tokenOptimizer.estimateTokenCount(request.prompt, request.model)
      const completionTokens = response.tokensUsed - promptTokens

      await saveTokenUsage({
        provider: response.provider,
        model: response.model,
        promptTokens: Math.max(0, promptTokens),
        completionTokens: Math.max(0, completionTokens),
        totalTokens: response.tokensUsed,
        cost: response.cost,
        user_id: userId
      })
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.warn('Failed to track token usage:', error)
      }
    }
  }

  // Batch processing for multiple requests
  async processBatch(
    requests: AIRequest[],
    userId: string,
    settings: AISettings,
    onProgress?: (completed: number, total: number) => void
  ): Promise<AIResponse[]> {
    const responses: AIResponse[] = []
    
    for (let i = 0; i < requests.length; i++) {
      const response = await this.generateOptimizedResponse(requests[i], userId, settings)
      responses.push(response)
      
      if (onProgress) {
        onProgress(i + 1, requests.length)
      }

      // Add small delay between requests to avoid rate limits
      if (i < requests.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    return responses
  }

  // Get cost estimate for a request
  estimateCost(request: AIRequest): number {
    const tokenCount = this.tokenOptimizer.estimateTokenCount(request.prompt, request.model)
    const estimatedResponseTokens = request.maxTokens || 1000
    const totalTokens = tokenCount + estimatedResponseTokens
    
    return aiProviderManager.calculateCost(request.provider, request.model, totalTokens)
  }
}

// Export singleton instance
export const optimizedAIManager = new OptimizedAIManager()

// Utility functions
export const getTokenOptimizer = () => TokenOptimizer.getInstance()

export const estimateRequestCost = (request: AIRequest): number => {
  return optimizedAIManager.estimateCost(request)
}

export const optimizePromptLength = (prompt: string, maxTokens: number): string => {
  return TokenOptimizer.getInstance().optimizePrompt(prompt, maxTokens)
}
