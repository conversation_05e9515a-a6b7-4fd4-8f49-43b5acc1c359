'use client'

import { useState } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { useAdminProfile } from '@/hooks/useAdminProfile'
import { updateProfile } from 'firebase/auth'
import {
  UserIcon,
  EnvelopeIcon,
  KeyIcon,
  CheckIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline'

export default function SettingsPage() {
  const { user, resetPassword } = useAuth()
  const { refreshProfile, refreshing, avatarSrc } = useAdminProfile()
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [formData, setFormData] = useState({
    displayName: user?.displayName || '',
    email: user?.email || '',
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    setLoading(true)
    setMessage('')

    try {
      await updateProfile(user, {
        displayName: formData.displayName
      })
      setMessage('Profile updated successfully!')
    } catch (error) {
      console.error('Error updating profile:', error)
      setMessage('Failed to update profile')
    } finally {
      setLoading(false)
    }
  }

  const handlePasswordReset = async () => {
    if (!user?.email) return

    setLoading(true)
    setMessage('')

    try {
      await resetPassword(user.email)
      setMessage('Password reset email sent! Check your inbox.')
    } catch (error) {
      console.error('Error sending password reset:', error)
      setMessage('Failed to send password reset email')
    } finally {
      setLoading(false)
    }
  }

  const handleRefreshProfile = async () => {
    setMessage('')
    try {
      await refreshProfile()
      setMessage('Profile picture refreshed successfully! The page will reload to show changes.')
      // Reload the page to ensure all components get the fresh data
      setTimeout(() => {
        window.location.reload()
      }, 1500)
    } catch (error) {
      console.error('Error refreshing profile:', error)
      setMessage('Failed to refresh profile picture')
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold dashboard-text">
          Settings
        </h1>
        <p className="dashboard-muted mt-2">
          Manage your account and preferences
        </p>
      </div>

      {/* Message */}
      {message && (
        <div className={`p-4 rounded-lg ${
          message.includes('successfully') || message.includes('sent')
            ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-200'
            : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200'
        }`}>
          <div className="flex items-center">
            <CheckIcon className="w-5 h-5 mr-2" />
            {message}
          </div>
        </div>
      )}

      {/* Profile Section */}
      <div className="dashboard-card rounded-lg shadow-lg p-8">
        <div className="flex items-center mb-8">
          <UserIcon className="w-6 h-6 dashboard-muted mr-3" />
          <h2 className="text-xl font-semibold dashboard-text">
            Profile
          </h2>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Avatar */}
          <div className="lg:col-span-1">
            <div className="text-center">
              <img
                src={avatarSrc}
                alt="Profile Picture"
                className="w-24 h-24 rounded-full object-cover border-4 border-gray-200 dark:border-gray-600 mx-auto mb-4"
              />
              <h3 className="text-sm font-medium dashboard-text mb-2">
                Profile Picture
              </h3>
              <p className="text-xs dashboard-muted mb-4">
                Synced from Google
              </p>
              <button
                onClick={handleRefreshProfile}
                disabled={refreshing}
                className="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center mx-auto"
                style={{ color: 'white' }}
              >
                <ArrowPathIcon className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                {refreshing ? 'Refreshing...' : 'Refresh'}
              </button>
            </div>
          </div>

          {/* Profile Form */}
          <div className="lg:col-span-2">
            <form onSubmit={handleUpdateProfile} className="space-y-6">
              <div>
                <label htmlFor="displayName" className="block text-sm font-medium dashboard-text mb-2">
                  Display Name
                </label>
                <input
                  type="text"
                  id="displayName"
                  name="displayName"
                  value={formData.displayName}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dashboard-text"
                  placeholder="Your display name"
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium dashboard-text mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  disabled
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-600 dashboard-muted cursor-not-allowed"
                />
                <p className="text-xs dashboard-muted mt-1">
                  Email cannot be changed
                </p>
              </div>

              <button
                type="submit"
                disabled={loading}
                className="px-6 py-3 bg-blue-600 text-white hover:bg-blue-700 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                style={{ color: 'white' }}
              >
                {loading ? 'Updating...' : 'Update Profile'}
              </button>
            </form>
          </div>
        </div>
      </div>

      {/* Security Section */}
      <div className="dashboard-card rounded-lg shadow-lg p-8">
        <div className="flex items-center mb-6">
          <KeyIcon className="w-6 h-6 dashboard-muted mr-3" />
          <h2 className="text-xl font-semibold dashboard-text">
            Security & Account
          </h2>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 className="text-sm font-medium dashboard-text mb-2">
              Password Reset
            </h3>
            <p className="text-sm dashboard-muted mb-4">
              Send a password reset link to your email address
            </p>
            <button
              onClick={handlePasswordReset}
              disabled={loading}
              className="px-4 py-2 bg-gray-600 text-white hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              style={{ color: 'white' }}
            >
              {loading ? 'Sending...' : 'Send Reset Email'}
            </button>
          </div>

          <div>
            <h3 className="text-sm font-medium dashboard-text mb-4">
              Account Status
            </h3>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between items-center">
                <span className="dashboard-muted">Email Verified:</span>
                <span className={user?.emailVerified ? 'text-green-600 dark:text-green-400' : 'text-yellow-600 dark:text-yellow-400'}>
                  {user?.emailVerified ? '✓ Verified' : '⚠ Not verified'}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="dashboard-muted">Account Created:</span>
                <span className="dashboard-text">
                  {user?.metadata?.creationTime ?
                    new Date(user.metadata.creationTime).toLocaleDateString() :
                    'Unknown'
                  }
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="dashboard-muted">Last Sign In:</span>
                <span className="dashboard-text">
                  {user?.metadata?.lastSignInTime ?
                    new Date(user.metadata.lastSignInTime).toLocaleDateString() :
                    'Unknown'
                  }
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
