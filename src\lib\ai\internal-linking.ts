// Internal Linking System
'use client'

import { InternalLink, AIRequest, AISettings } from '@/types/ai'
import { sitemapAnalyzer, SitemapEntry } from './sitemap'
import { optimizedAIManager } from './optimization'

export interface LinkingStrategy {
  maxLinksPerSection: number
  minRelevanceScore: number
  preferRecentContent: boolean
  avoidOverLinking: boolean
  contextualPlacement: boolean
  diversifyLinkTypes: boolean
}

export interface LinkingContext {
  currentContent: string
  keywords: string[]
  categories: string[]
  tags: string[]
  targetAudience: string
}

export interface SmartLink {
  anchorText: string
  targetUrl: string
  targetTitle: string
  relevanceScore: number
  context: string
  position: number
  linkType: 'blog' | 'project' | 'page'
  reasoning: string
}

// Internal Linking Engine
export class InternalLinkingEngine {
  private static instance: InternalLinkingEngine

  static getInstance(): InternalLinkingEngine {
    if (!InternalLinkingEngine.instance) {
      InternalLinkingEngine.instance = new InternalLinkingEngine()
    }
    return InternalLinkingEngine.instance
  }

  // Main method to add intelligent internal links
  async addIntelligentLinks(
    content: string,
    context: LinkingContext,
    strategy: LinkingStrategy = {
      maxLinksPerSection: 3,
      minRelevanceScore: 0.4,
      preferRecentContent: true,
      avoidOverLinking: true,
      contextualPlacement: true,
      diversifyLinkTypes: true
    },
    settings: AISettings,
    userId: string
  ): Promise<string> {
    try {
      // Get available content for linking
      const sitemap = await sitemapAnalyzer.generateSitemap()
      
      // Find potential linking opportunities
      const opportunities = await this.findLinkingOpportunities(
        content,
        context,
        sitemap,
        strategy
      )

      // Use AI to optimize link placement and anchor text
      const optimizedLinks = await this.optimizeLinkPlacement(
        content,
        opportunities,
        settings,
        userId
      )

      // Insert links into content
      const linkedContent = this.insertLinksIntoContent(
        content,
        optimizedLinks,
        strategy
      )

      return linkedContent

    } catch (error) {
      console.error('Internal linking failed:', error)
      return content // Return original content if linking fails
    }
  }

  private async findLinkingOpportunities(
    content: string,
    context: LinkingContext,
    sitemap: SitemapEntry[],
    strategy: LinkingStrategy
  ): Promise<SmartLink[]> {
    const opportunities: SmartLink[] = []
    const contentSections = this.splitContentIntoSections(content)

    for (const entry of sitemap) {
      // Skip if not enough relevance
      const relevanceScore = this.calculateRelevance(entry, context)
      if (relevanceScore < strategy.minRelevanceScore) continue

      // Find potential anchor texts and contexts
      const linkOpportunities = this.findAnchorOpportunities(
        contentSections,
        entry,
        relevanceScore
      )

      opportunities.push(...linkOpportunities)
    }

    // Sort by relevance and apply strategy filters
    return this.applyLinkingStrategy(opportunities, strategy)
  }

  private calculateRelevance(entry: SitemapEntry, context: LinkingContext): number {
    let score = 0

    // Keyword matching
    const entryText = `${entry.title} ${entry.excerpt || ''} ${entry.tags?.join(' ') || ''}`
    context.keywords.forEach(keyword => {
      if (entryText.toLowerCase().includes(keyword.toLowerCase())) {
        score += 0.3
      }
    })

    // Category matching
    if (entry.categories) {
      context.categories.forEach(category => {
        if (entry.categories?.includes(category)) {
          score += 0.2
        }
      })
    }

    // Tag matching
    if (entry.tags) {
      context.tags.forEach(tag => {
        if (entry.tags?.includes(tag)) {
          score += 0.1
        }
      })
    }

    // Content type diversity bonus
    if (entry.type === 'project' && context.categories.includes('Technology')) {
      score += 0.1
    }

    // Recency bonus
    if (entry.lastModified) {
      const daysSinceUpdate = (Date.now() - new Date(entry.lastModified).getTime()) / (1000 * 60 * 60 * 24)
      if (daysSinceUpdate < 30) {
        score += 0.1
      }
    }

    return Math.min(score, 1.0)
  }

  private findAnchorOpportunities(
    contentSections: string[],
    entry: SitemapEntry,
    relevanceScore: number
  ): SmartLink[] {
    const opportunities: SmartLink[] = []
    const titleWords = entry.title.toLowerCase().split(' ')

    contentSections.forEach((section, sectionIndex) => {
      const sentences = section.split(/[.!?]+/).filter(s => s.trim().length > 0)

      sentences.forEach((sentence, sentenceIndex) => {
        const sentenceLower = sentence.toLowerCase()

        // Look for exact title matches
        if (sentenceLower.includes(entry.title.toLowerCase())) {
          opportunities.push({
            anchorText: entry.title,
            targetUrl: entry.url,
            targetTitle: entry.title,
            relevanceScore,
            context: sentence.trim(),
            position: sectionIndex * 1000 + sentenceIndex,
            linkType: entry.type,
            reasoning: 'Exact title match'
          })
        }

        // Look for partial title matches (2+ words)
        for (let i = titleWords.length; i >= 2; i--) {
          const partialTitle = titleWords.slice(0, i).join(' ')
          if (sentenceLower.includes(partialTitle)) {
            opportunities.push({
              anchorText: partialTitle,
              targetUrl: entry.url,
              targetTitle: entry.title,
              relevanceScore: relevanceScore * 0.8,
              context: sentence.trim(),
              position: sectionIndex * 1000 + sentenceIndex,
              linkType: entry.type,
              reasoning: 'Partial title match'
            })
            break
          }
        }

        // Look for tag/category matches
        if (entry.tags) {
          entry.tags.forEach(tag => {
            if (sentenceLower.includes(tag.toLowerCase()) && tag.length > 3) {
              opportunities.push({
                anchorText: tag,
                targetUrl: entry.url,
                targetTitle: entry.title,
                relevanceScore: relevanceScore * 0.6,
                context: sentence.trim(),
                position: sectionIndex * 1000 + sentenceIndex,
                linkType: entry.type,
                reasoning: 'Tag match'
              })
            }
          })
        }
      })
    })

    return opportunities
  }

  private async optimizeLinkPlacement(
    content: string,
    opportunities: SmartLink[],
    settings: AISettings,
    userId: string
  ): Promise<SmartLink[]> {
    if (opportunities.length === 0) return []

    const optimizationPrompt = `
Analyze these internal linking opportunities and optimize them for better user experience:

Content excerpt: ${content.substring(0, 1000)}...

Linking opportunities:
${opportunities.slice(0, 10).map((opp, index) => 
  `${index + 1}. "${opp.anchorText}" → ${opp.targetTitle} (Score: ${opp.relevanceScore.toFixed(2)})`
).join('\n')}

Instructions:
1. Select the most valuable links (max 8)
2. Ensure natural anchor text that flows with the content
3. Avoid over-linking in any single paragraph
4. Prioritize links that add genuine value to readers
5. Suggest better anchor text if needed

Respond with a JSON array of optimized links:
[
  {
    "anchorText": "optimized anchor text",
    "targetUrl": "/target/url",
    "targetTitle": "Target Title",
    "relevanceScore": 0.8,
    "reasoning": "why this link is valuable"
  }
]
`

    const request: AIRequest = {
      provider: settings.provider,
      model: settings.models.content,
      prompt: optimizationPrompt,
      maxTokens: 1500,
      temperature: 0.3,
      systemPrompt: 'You are an SEO expert optimizing internal links for better user experience and search rankings.'
    }

    try {
      const response = await optimizedAIManager.generateOptimizedResponse(request, userId, settings)
      const optimizedData = JSON.parse(response.content)
      
      // Map back to SmartLink format
      return optimizedData.map((link: any, index: number) => ({
        ...opportunities[index],
        anchorText: link.anchorText,
        reasoning: link.reasoning || opportunities[index].reasoning
      }))
    } catch (error) {
      console.error('Failed to optimize link placement:', error)
      // Return top opportunities as fallback
      return opportunities.slice(0, 8)
    }
  }

  private applyLinkingStrategy(
    opportunities: SmartLink[],
    strategy: LinkingStrategy
  ): SmartLink[] {
    let filtered = opportunities
      .filter(opp => opp.relevanceScore >= strategy.minRelevanceScore)
      .sort((a, b) => b.relevanceScore - a.relevanceScore)

    // Diversify link types if requested
    if (strategy.diversifyLinkTypes) {
      filtered = this.diversifyLinkTypes(filtered)
    }

    // Prefer recent content if requested
    if (strategy.preferRecentContent) {
      filtered = filtered.sort((a, b) => {
        if (a.linkType === 'blog' && b.linkType !== 'blog') return -1
        if (b.linkType === 'blog' && a.linkType !== 'blog') return 1
        return b.relevanceScore - a.relevanceScore
      })
    }

    // Limit total links
    return filtered.slice(0, strategy.maxLinksPerSection * 3)
  }

  private diversifyLinkTypes(opportunities: SmartLink[]): SmartLink[] {
    const diversified: SmartLink[] = []
    const typeCount = { blog: 0, project: 0, page: 0 }
    const maxPerType = 3

    for (const opp of opportunities) {
      if (typeCount[opp.linkType] < maxPerType) {
        diversified.push(opp)
        typeCount[opp.linkType]++
      }
    }

    return diversified
  }

  private insertLinksIntoContent(
    content: string,
    links: SmartLink[],
    strategy: LinkingStrategy
  ): string {
    let modifiedContent = content
    const insertedAnchors = new Set<string>()

    // Sort links by position (reverse order to maintain positions)
    const sortedLinks = links.sort((a, b) => b.position - a.position)

    for (const link of sortedLinks) {
      // Avoid duplicate anchor texts
      if (insertedAnchors.has(link.anchorText.toLowerCase())) continue

      // Create markdown link
      const markdownLink = `[${link.anchorText}](${link.targetUrl})`
      
      // Find and replace the anchor text (case insensitive, whole words only)
      const regex = new RegExp(`\\b${this.escapeRegex(link.anchorText)}\\b`, 'i')
      const match = modifiedContent.match(regex)
      
      if (match && !modifiedContent.includes(markdownLink)) {
        modifiedContent = modifiedContent.replace(regex, markdownLink)
        insertedAnchors.add(link.anchorText.toLowerCase())
      }
    }

    return modifiedContent
  }

  private splitContentIntoSections(content: string): string[] {
    // Split by headers and paragraphs
    return content
      .split(/\n#{2,4}\s+/)
      .filter(section => section.trim().length > 0)
  }

  private escapeRegex(text: string): string {
    return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }

  // Generate related content suggestions
  async generateRelatedContentSuggestions(
    content: string,
    context: LinkingContext,
    maxSuggestions: number = 5
  ): Promise<SitemapEntry[]> {
    try {
      const sitemap = await sitemapAnalyzer.generateSitemap()
      const suggestions: Array<SitemapEntry & { relevanceScore: number }> = []

      for (const entry of sitemap) {
        const relevanceScore = this.calculateRelevance(entry, context)
        if (relevanceScore > 0.3) {
          suggestions.push({ ...entry, relevanceScore })
        }
      }

      return suggestions
        .sort((a, b) => b.relevanceScore - a.relevanceScore)
        .slice(0, maxSuggestions)
        .map(({ relevanceScore, ...entry }) => entry)
    } catch (error) {
      console.error('Failed to generate related content suggestions:', error)
      return []
    }
  }

  // Analyze link density and suggest optimizations
  analyzeLinkDensity(content: string): {
    totalWords: number
    totalLinks: number
    linkDensity: number
    recommendation: string
  } {
    const words = content.split(/\s+/).length
    const links = (content.match(/\[([^\]]+)\]\([^)]+\)/g) || []).length
    const density = links / words

    let recommendation = 'Good'
    if (density > 0.05) {
      recommendation = 'Too many links - consider reducing'
    } else if (density < 0.01) {
      recommendation = 'Could benefit from more internal links'
    }

    return {
      totalWords: words,
      totalLinks: links,
      linkDensity: density,
      recommendation
    }
  }
}

// Export singleton instance
export const internalLinkingEngine = InternalLinkingEngine.getInstance()

// Utility functions
export const addSmartInternalLinks = async (
  content: string,
  context: LinkingContext,
  settings: AISettings,
  userId: string,
  strategy?: LinkingStrategy
): Promise<string> => {
  return await internalLinkingEngine.addIntelligentLinks(
    content,
    context,
    strategy,
    settings,
    userId
  )
}

export const getRelatedContentSuggestions = async (
  content: string,
  context: LinkingContext,
  maxSuggestions?: number
): Promise<SitemapEntry[]> => {
  return await internalLinkingEngine.generateRelatedContentSuggestions(
    content,
    context,
    maxSuggestions
  )
}

export const analyzeLinkDensity = (content: string) => {
  return internalLinkingEngine.analyzeLinkDensity(content)
}
