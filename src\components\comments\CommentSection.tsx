'use client'

import { useState } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { DatabaseComment } from '@/types'
import CommentLogin from './CommentLogin'
import CommentForm from './CommentForm'
import ThreadedCommentList from './ThreadedCommentList'
import Link from 'next/link'

interface CommentSectionProps {
  postId: string
  postTitle: string
  authorId: string
}

const ADMIN_UID = 'KNlrg408xubJeEmwFpUbeDQWBgF3'

export default function CommentSection({ postId, postTitle, authorId }: CommentSectionProps) {
  const { user, loading } = useAuth()
  const [newComment, setNewComment] = useState<DatabaseComment | null>(null)

  const isAdmin = user?.uid === ADMIN_UID

  const handleCommentSubmitted = (comment: DatabaseComment) => {
    setNewComment(comment)
    // Clear the new comment after a short delay to avoid duplicate displays
    setTimeout(() => setNewComment(null), 1000)
  }

  const handleLoginSuccess = () => {
    // Optionally scroll to comment form or show a success message
    // User can now comment after successful login
  }

  if (loading) {
    return (
      <div className="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Comments
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              Share your thoughts about "{postTitle}"
            </p>
          </div>

          {/* Admin Dashboard Link */}
          {isAdmin && (
            <Link
              href="/dashboard/comments"
              className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              Admin Panel
            </Link>
          )}
        </div>
      </div>

      {/* Comment Form or Login Prompt */}
      <div className="mb-8">
        {user ? (
          <CommentForm 
            postId={postId} 
            onCommentSubmitted={handleCommentSubmitted}
          />
        ) : (
          <CommentLogin onLoginSuccess={handleLoginSuccess} />
        )}
      </div>

      {/* Comments List */}
      <ThreadedCommentList
        postId={postId}
        authorId={authorId}
        newComment={newComment}
      />
    </div>
  )
}
