﻿'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/providers/AuthProvider'

const ADMIN_UID = 'KNlrg408xubJeEmwFpUbeDQWBgF3'

export default function LoginPage() {
  const router = useRouter()
  const { user, loading } = useAuth()

  useEffect(() => {
    if (!loading) {
      if (!user) {
        // Non-logged users redirect to homepage
        router.replace('/')
      } else if (user.uid === ADMIN_UID) {
        // Admin user redirects to dashboard
        router.replace('/dashboard')
      } else {
        // Logged non-admin users redirect to homepage
        router.replace('/')
      }
    }
  }, [user, loading, router])

  // Show loading while redirecting
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-4 text-gray-600 dark:text-gray-400">Redirecting...</p>
      </div>
    </div>
  )
}
