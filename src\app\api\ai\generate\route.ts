// AI Content Generation API Endpoint
import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { generateBlogContent, getOrCreateAISettings, validateContentQuality } from '@/lib/ai'
import { verifyIdToken } from '@/lib/firebase-admin'
import { BlogOutline, ResearchResult } from '@/types/ai'

// Rate limiting for content generation (more restrictive due to cost)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

const RATE_LIMIT = {
  maxRequests: 5, // Lower limit for expensive operations
  windowMs: 60 * 60 * 1000, // 1 hour
}

function checkRateLimit(userId: string): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now()
  const userLimit = rateLimitStore.get(userId)

  if (!userLimit || now > userLimit.resetTime) {
    const resetTime = now + RATE_LIMIT.windowMs
    rateLimitStore.set(userId, { count: 1, resetTime })
    return { allowed: true, remaining: RATE_LIMIT.maxRequests - 1, resetTime }
  }

  if (userLimit.count >= RATE_LIMIT.maxRequests) {
    return { allowed: false, remaining: 0, resetTime: userLimit.resetTime }
  }

  userLimit.count++
  rateLimitStore.set(userId, userLimit)
  
  return { 
    allowed: true, 
    remaining: RATE_LIMIT.maxRequests - userLimit.count, 
    resetTime: userLimit.resetTime 
  }
}

export async function POST(request: NextRequest) {
  try {
    // Authentication
    const headersList = headers()
    const authorization = headersList.get('authorization')
    
    if (!authorization?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401 }
      )
    }

    const token = authorization.split('Bearer ')[1]
    const decodedToken = await verifyIdToken(token)
    const userId = decodedToken.uid

    // Rate limiting
    const rateLimit = checkRateLimit(userId)
    if (!rateLimit.allowed) {
      return NextResponse.json(
        { 
          error: 'Rate limit exceeded. Content generation is limited to preserve costs.',
          resetTime: rateLimit.resetTime
        },
        { 
          status: 429,
          headers: {
            'X-RateLimit-Limit': RATE_LIMIT.maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': rateLimit.resetTime.toString()
          }
        }
      )
    }

    // Parse and validate request
    const body = await request.json()
    const { outline, researchResult, options = {} } = body

    // Validate required fields
    if (!outline || typeof outline !== 'object') {
      return NextResponse.json(
        { error: 'Blog outline is required' },
        { status: 400 }
      )
    }

    if (!researchResult || typeof researchResult !== 'object') {
      return NextResponse.json(
        { error: 'Research result is required' },
        { status: 400 }
      )
    }

    // Validate outline structure
    const requiredOutlineFields = ['title', 'chapters', 'metadata']
    for (const field of requiredOutlineFields) {
      if (!(field in outline)) {
        return NextResponse.json(
          { error: `Outline missing required field: ${field}` },
          { status: 400 }
        )
      }
    }

    // Validate generation options
    const generationOptions = {
      includeIntroduction: options.includeIntroduction !== false,
      includeConclusion: options.includeConclusion !== false,
      enableInternalLinking: options.enableInternalLinking !== false,
      enableExternalCitations: options.enableExternalCitations !== false,
      maxLinksPerSection: Math.min(Math.max(options.maxLinksPerSection || 3, 1), 10),
      writingStyle: ['professional', 'casual', 'technical', 'conversational'].includes(options.writingStyle)
        ? options.writingStyle
        : 'professional',
      targetAudience: options.targetAudience || 'intermediate developers'
    }

    // Get user settings and check cost limits
    const settings = await getOrCreateAISettings(userId)
    
    // Estimate cost before generation
    const estimatedTokens = outline.metadata.estimatedWordCount * 1.5
    const estimatedCost = estimatedTokens * 0.002 / 1000 // Rough estimate
    
    if (estimatedCost > settings.preferences.costLimit * 0.5) {
      return NextResponse.json(
        { 
          error: 'Estimated generation cost exceeds 50% of monthly limit',
          estimatedCost,
          monthlyLimit: settings.preferences.costLimit
        },
        { status: 402 }
      )
    }

    // Set up progress tracking (in production, use WebSockets or Server-Sent Events)
    const progressCallback = (progress: any) => {
      // For now, we'll just log progress
      console.log(`Generation Progress: ${progress.progress}% - ${progress.message}`)
    }

    // Generate content
    const generatedContent = await generateBlogContent(
      outline as BlogOutline,
      researchResult as ResearchResult,
      userId,
      generationOptions,
      progressCallback
    )

    // Validate generated content quality
    const qualityValidation = await validateContentQuality(
      generatedContent.content,
      generatedContent.title,
      {
        excerpt: generatedContent.excerpt,
        tags: generatedContent.metadata.tags,
        categories: generatedContent.metadata.categories,
        focusKeyword: outline.metadata.seoKeywords?.[0]
      },
      settings,
      userId,
      {
        strictMode: false,
        targetAudience: generationOptions.targetAudience as any,
        contentType: 'guide',
        minWordCount: 800,
        maxWordCount: 8000,
        requireImages: false,
        requireCodeExamples: false
      }
    )

    return NextResponse.json(
      {
        success: true,
        data: {
          ...generatedContent,
          qualityScore: qualityValidation.score,
          qualityIssues: qualityValidation.issues.filter(issue => issue.severity === 'high'),
          isPublishReady: qualityValidation.isPublishReady
        },
        metadata: {
          tokensUsed: generatedContent.tokensUsed,
          cost: generatedContent.cost,
          estimatedReadingTime: generatedContent.metadata.estimatedReadingTime,
          wordCount: generatedContent.metadata.wordCount,
          timestamp: new Date().toISOString()
        }
      },
      {
        status: 200,
        headers: {
          'X-RateLimit-Limit': RATE_LIMIT.maxRequests.toString(),
          'X-RateLimit-Remaining': rateLimit.remaining.toString(),
          'X-RateLimit-Reset': rateLimit.resetTime.toString()
        }
      }
    )

  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('AI Content Generation API Error:', error)
    }

    if (error instanceof Error) {
      if (error.message.includes('Cost limit exceeded')) {
        return NextResponse.json(
          { error: 'Cost limit exceeded', details: error.message },
          { status: 402 }
        )
      }

      if (error.message.includes('Invalid token')) {
        return NextResponse.json(
          { error: 'Invalid authentication token' },
          { status: 401 }
        )
      }

      if (error.message.includes('Content generation failed')) {
        return NextResponse.json(
          { error: 'Content generation failed', details: error.message },
          { status: 500 }
        )
      }
    }

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: process.env.NODE_ENV === 'development' ? (error as Error).message : 'An unexpected error occurred'
      },
      { status: 500 }
    )
  }
}

// GET endpoint for generation status and cost estimates
export async function GET(request: NextRequest) {
  try {
    const headersList = headers()
    const authorization = headersList.get('authorization')
    
    if (!authorization?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401 }
      )
    }

    const token = authorization.split('Bearer ')[1]
    const decodedToken = await verifyIdToken(token)
    const userId = decodedToken.uid

    const { searchParams } = new URL(request.url)
    const wordCount = parseInt(searchParams.get('wordCount') || '2000')
    const provider = searchParams.get('provider') || 'gemini'

    // Get user settings
    const settings = await getOrCreateAISettings(userId)
    
    // Calculate cost estimates
    const estimatedTokens = wordCount * 1.5
    const costPerToken = {
      openai: 0.002 / 1000,
      gemini: 0.00125 / 1000,
      openrouter: 0.002 / 1000
    }
    
    const estimatedCost = estimatedTokens * (costPerToken[provider as keyof typeof costPerToken] || costPerToken.gemini)
    
    // Get rate limit status
    const rateLimit = checkRateLimit(userId)

    return NextResponse.json({
      estimates: {
        wordCount,
        estimatedTokens,
        estimatedCost,
        estimatedTime: Math.ceil(wordCount / 500) * 2, // 2 minutes per 500 words
        provider
      },
      rateLimit: {
        limit: RATE_LIMIT.maxRequests,
        remaining: rateLimit.remaining,
        resetTime: rateLimit.resetTime
      },
      userLimits: {
        monthlyLimit: settings.preferences.costLimit,
        currentUsage: 0 // TODO: Implement actual usage tracking
      },
      status: 'ready'
    })

  } catch (error) {
    console.error('Generation Status Error:', error)
    return NextResponse.json(
      { error: 'Failed to get generation status' },
      { status: 500 }
    )
  }
}
