'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { getEngagementAnalytics, getBlogPosts, getProjects } from '@/lib/firebase-operations'
import { DatabaseBlogPost, DatabaseProject } from '@/types'
import { 
  MessageCircleIcon, 
  HeartIcon, 
  EyeIcon, 
  TrendingUpIcon,
  UsersIcon,
  ActivityIcon,
  BarChart3Icon,
  CalendarIcon
} from 'lucide-react'

interface AnalyticsData {
  totalComments: number
  totalHearts: number
  totalViews: number
  commentsByPost: { [postId: string]: number }
  heartsByPost: { [postId: string]: number }
  viewsBySlug: { [slug: string]: number }
  recentActivity: Array<{
    type: 'comment' | 'heart' | 'view'
    postId?: string
    slug?: string
    timestamp: string
    count?: number
  }>
}

interface PostWithEngagement extends DatabaseBlogPost {
  comments: number
  hearts: number
  views: number
}

export default function AnalyticsDashboard() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [posts, setPosts] = useState<PostWithEngagement[]>([])
  const [projects, setProjects] = useState<DatabaseProject[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadAnalytics()
  }, [])

  const loadAnalytics = async () => {
    try {
      setLoading(true)
      
      // Load analytics data
      const analyticsData = await getEngagementAnalytics()
      setAnalytics(analyticsData)
      
      // Load posts and projects
      const [postsData, projectsData] = await Promise.all([
        getBlogPosts(),
        getProjects()
      ])
      
      // Combine posts with engagement data
      const postsWithEngagement = postsData.map(post => ({
        ...post,
        comments: analyticsData.commentsByPost[post.id] || 0,
        hearts: analyticsData.heartsByPost[post.id] || 0,
        views: analyticsData.viewsBySlug[post.slug] || 0
      }))
      
      setPosts(postsWithEngagement)
      setProjects(projectsData)
    } catch (error) {
      console.error('Error loading analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
    return num.toString()
  }

  const getEngagementRate = (comments: number, hearts: number, views: number) => {
    if (views === 0) return 0
    return Math.round(((comments + hearts) / views) * 100)
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-4 bg-muted rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-muted rounded w-1/3 mb-2"></div>
                <div className="h-3 bg-muted rounded w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (!analytics) return null

  const topPosts = posts
    .sort((a, b) => (b.comments + b.hearts + b.views) - (a.comments + a.hearts + a.views))
    .slice(0, 5)

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
          <p className="text-muted-foreground mt-1">
            Track engagement and performance across your content
          </p>
        </div>
        <Badge variant="outline" className="flex items-center gap-2">
          <ActivityIcon className="w-4 h-4" />
          Live Data
        </Badge>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <EyeIcon className="w-4 h-4" />
              Total Views
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(analytics.totalViews)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Across all blog posts
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <MessageCircleIcon className="w-4 h-4" />
              Total Comments
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(analytics.totalComments)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Community engagement
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-red-500">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <HeartIcon className="w-4 h-4" />
              Total Hearts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(analytics.totalHearts)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Reactions received
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <TrendingUpIcon className="w-4 h-4" />
              Engagement Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics.totalViews > 0 
                ? Math.round(((analytics.totalComments + analytics.totalHearts) / analytics.totalViews) * 100)
                : 0}%
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Comments + Hearts / Views
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Content Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Performing Posts */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3Icon className="w-5 h-5" />
              Top Performing Posts
            </CardTitle>
            <CardDescription>
              Posts ranked by total engagement (views + comments + hearts)
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {topPosts.length > 0 ? (
              topPosts.map((post, index) => {
                const totalEngagement = post.views + post.comments + post.hearts
                const maxEngagement = Math.max(...topPosts.map(p => p.views + p.comments + p.hearts))
                const percentage = maxEngagement > 0 ? (totalEngagement / maxEngagement) * 100 : 0
                
                return (
                  <div key={post.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Badge variant="outline" className="w-6 h-6 rounded-full p-0 flex items-center justify-center text-xs">
                          {index + 1}
                        </Badge>
                        <div>
                          <p className="font-medium text-sm line-clamp-1">{post.title}</p>
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <EyeIcon className="w-3 h-3" />
                              {post.views}
                            </span>
                            <span className="flex items-center gap-1">
                              <MessageCircleIcon className="w-3 h-3" />
                              {post.comments}
                            </span>
                            <span className="flex items-center gap-1">
                              <HeartIcon className="w-3 h-3" />
                              {post.hearts}
                            </span>
                          </div>
                        </div>
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        {getEngagementRate(post.comments, post.hearts, post.views)}%
                      </Badge>
                    </div>
                    <Progress value={percentage} className="h-2" />
                  </div>
                )
              })
            ) : (
              <p className="text-muted-foreground text-center py-8">
                No posts with engagement data yet
              </p>
            )}
          </CardContent>
        </Card>

        {/* Content Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UsersIcon className="w-5 h-5" />
              Content Overview
            </CardTitle>
            <CardDescription>
              Summary of your published content
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{posts.length}</div>
                <p className="text-sm text-muted-foreground">Blog Posts</p>
              </div>
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{projects.length}</div>
                <p className="text-sm text-muted-foreground">Projects</p>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">Published Posts</span>
                <span className="text-sm font-medium">
                  {posts.filter(p => p.published).length} / {posts.length}
                </span>
              </div>
              <Progress 
                value={posts.length > 0 ? (posts.filter(p => p.published).length / posts.length) * 100 : 0} 
                className="h-2" 
              />
              
              <div className="flex justify-between items-center">
                <span className="text-sm">Published Projects</span>
                <span className="text-sm font-medium">
                  {projects.filter(p => p.published).length} / {projects.length}
                </span>
              </div>
              <Progress 
                value={projects.length > 0 ? (projects.filter(p => p.published).length / projects.length) * 100 : 0} 
                className="h-2" 
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
