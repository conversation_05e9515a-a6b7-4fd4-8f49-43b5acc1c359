'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { useRouter } from 'next/navigation'
import { 
  DocumentTextIcon, 
  PencilIcon,
  CheckIcon,
  XMarkIcon,
  SparklesIcon,
  ClockIcon
} from '@heroicons/react/24/outline'
import {
  DashboardCard,
  DashboardCardContent,
  DashboardCardDescription,
  DashboardCardHeader,
  DashboardCardTitle
} from '@/components/dashboard/DashboardCard'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  getOrCreateAISettings,
  generateBlogOutline,
  outlineGenerator
} from '@/lib/ai'
import { AISettings, ResearchResult, BlogOutline, DEFAULT_AI_SETTINGS } from '@/types/ai'

export default function OutlinePage() {
  const { user } = useAuth()
  const router = useRouter()
  const [settings, setSettings] = useState<AISettings | null>(null)
  const [researchResult, setResearchResult] = useState<ResearchResult | null>(null)
  const [keywords, setKeywords] = useState<string[]>([])
  const [researchOptions, setResearchOptions] = useState<any>(null)
  const [outline, setOutline] = useState<BlogOutline | null>(null)
  const [generating, setGenerating] = useState(false)
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(true)
  const [editingSection, setEditingSection] = useState<string | null>(null)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      // Load research data from session storage
      const storedResearch = sessionStorage.getItem('ai-research-result')
      const storedKeywords = sessionStorage.getItem('ai-keywords')
      const storedOptions = sessionStorage.getItem('ai-research-options')

      if (!storedResearch || !storedKeywords) {
        setError('No research data found. Please start from the research step.')
        return
      }

      setResearchResult(JSON.parse(storedResearch))
      setKeywords(JSON.parse(storedKeywords))
      setResearchOptions(storedOptions ? JSON.parse(storedOptions) : {})

      // Load AI settings with fallback
      if (user) {
        try {
          const userSettings = await getOrCreateAISettings(user.uid)
          setSettings(userSettings)
        } catch (error) {
          console.warn('Using default AI settings for outline:', error)
          const defaultSettings = {
            ...DEFAULT_AI_SETTINGS,
            user_id: user.uid,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
          setSettings(defaultSettings)
        }
      }
    } catch (error) {
      console.error('Error loading data:', error)
      setError('Failed to load research data')
    } finally {
      setLoading(false)
    }
  }

  const generateOutline = async () => {
    if (!user || !settings || !researchResult) return

    setGenerating(true)
    setError('')

    try {
      const outlineOptions = {
        targetWordCount: 2000,
        targetAudience: researchOptions?.targetAudience || 'intermediate developers',
        contentType: researchOptions?.contentType || 'guide',
        includeIntroConclusion: true,
        maxChapters: 6
      }

      try {
        const generatedOutline = await generateBlogOutline(
          keywords,
          researchResult,
          user.uid,
          outlineOptions
        )

        setOutline(generatedOutline)

        // Store outline for next step
        sessionStorage.setItem('ai-outline', JSON.stringify(generatedOutline))
      } catch (aiError) {
        console.warn('AI outline generation failed, creating mock outline:', aiError)

        // Create mock outline for testing
        const mockOutline: BlogOutline = {
          title: `Complete Guide to ${keywords.join(' and ')}`,
          introduction: {
            title: 'Introduction',
            content: `Welcome to this comprehensive guide on ${keywords.join(', ')}. In this article, we'll explore the fundamentals, best practices, and advanced techniques.`,
            wordCount: 150
          },
          chapters: [
            {
              title: `Understanding ${keywords[0]}`,
              sections: [
                {
                  title: 'What is ' + keywords[0] + '?',
                  content: `Learn the basics and core concepts of ${keywords[0]}.`,
                  estimatedWordCount: 300
                },
                {
                  title: 'Key Benefits and Features',
                  content: `Discover the main advantages and features that make ${keywords[0]} powerful.`,
                  estimatedWordCount: 250
                }
              ],
              estimatedWordCount: 550,
              includeCodeBlock: true,
              includeTable: false,
              includeQuote: false
            },
            {
              title: 'Getting Started',
              sections: [
                {
                  title: 'Setup and Installation',
                  content: `Step-by-step guide to setting up ${keywords[0]} in your development environment.`,
                  estimatedWordCount: 400
                },
                {
                  title: 'Basic Configuration',
                  content: `Learn how to configure ${keywords[0]} for your specific needs.`,
                  estimatedWordCount: 300
                }
              ],
              estimatedWordCount: 700,
              includeCodeBlock: true,
              includeTable: true,
              includeQuote: false
            },
            {
              title: 'Best Practices and Advanced Techniques',
              sections: [
                {
                  title: 'Performance Optimization',
                  content: `Techniques to optimize performance and improve efficiency.`,
                  estimatedWordCount: 350
                },
                {
                  title: 'Common Pitfalls and Solutions',
                  content: `Learn about common mistakes and how to avoid them.`,
                  estimatedWordCount: 300
                }
              ],
              estimatedWordCount: 650,
              includeCodeBlock: true,
              includeTable: false,
              includeQuote: true
            }
          ],
          conclusion: {
            title: 'Conclusion',
            content: `In this guide, we've covered the essential aspects of ${keywords.join(' and ')}. You now have the knowledge to implement these concepts effectively in your projects.`,
            wordCount: 100
          },
          metadata: {
            estimatedWordCount: 2000,
            targetAudience: outlineOptions.targetAudience,
            contentType: outlineOptions.contentType,
            seoKeywords: keywords,
            categories: ['Development', 'Tutorial'],
            tags: [...keywords, 'guide', 'tutorial', 'best practices']
          },
          internalLinks: [
            {
              text: keywords[0] + ' tutorial',
              url: '/blog/' + keywords[0].toLowerCase() + '-tutorial',
              relevanceScore: 0.9,
              context: 'Related tutorial content'
            }
          ]
        }

        setOutline(mockOutline)

        // Store mock outline for next step
        sessionStorage.setItem('ai-outline', JSON.stringify(mockOutline))

        setError('Using mock outline for testing. Configure AI providers for real outline generation.')
      }

    } catch (error) {
      console.error('Outline generation failed:', error)
      setError(`Outline generation failed: ${error}`)
    } finally {
      setGenerating(false)
    }
  }

  const proceedToGeneration = () => {
    router.push('/dashboard/ai-generator/generate')
  }

  const goBackToResearch = () => {
    router.push('/dashboard/ai-generator/research')
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading outline generator...</p>
        </div>
      </div>
    )
  }

  if (error && !researchResult) {
    return (
      <div className="text-center py-12">
        <XMarkIcon className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold mb-2">Missing Research Data</h2>
        <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
        <Button onClick={goBackToResearch}>
          ← Back to Research
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight dashboard-text flex items-center gap-2">
            <DocumentTextIcon className="w-7 h-7 text-purple-600" />
            Blog Outline
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Generate and review your AI-powered blog outline
          </p>
        </div>
        <Button variant="outline" onClick={goBackToResearch}>
          ← Back to Research
        </Button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-start">
            <XMarkIcon className="w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Outline Generation Error
              </h3>
              <p className="mt-1 text-sm text-red-700 dark:text-red-300">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Research Summary */}
      {researchResult && (
        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle>Research Summary</DashboardCardTitle>
            <DashboardCardDescription>
              Based on {researchResult.sources.length} sources • Keywords: {keywords.join(', ')}
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-medium mb-2">Key Points</h3>
                <ul className="text-sm text-gray-600 dark:text-gray-400 list-disc list-inside space-y-1">
                  {researchResult.keyPoints.slice(0, 4).map((point, index) => (
                    <li key={index}>{point}</li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="font-medium mb-2">Related Topics</h3>
                <div className="flex flex-wrap gap-2">
                  {researchResult.relatedTopics.slice(0, 6).map((topic, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {topic}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </DashboardCardContent>
        </DashboardCard>
      )}

      {/* Generated Outline */}
      {outline && (
        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle className="flex items-center gap-2">
              <SparklesIcon className="w-5 h-5 text-green-500" />
              Generated Outline
            </DashboardCardTitle>
            <DashboardCardDescription>
              Estimated {outline.metadata.estimatedWordCount} words • 
              {outlineGenerator.estimateReadingTime(outline)} min read
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent>
            <div className="space-y-6">
              {/* Title */}
              <div>
                <h2 className="text-xl font-bold mb-2">{outline.title}</h2>
              </div>

              {/* Introduction */}
              <div className="border-l-4 border-blue-500 pl-4">
                <h3 className="font-semibold text-blue-700 dark:text-blue-300 mb-2">
                  {outline.introduction.title}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  {outline.introduction.content}
                </p>
                <div className="flex items-center gap-2 text-xs text-gray-500">
                  <ClockIcon className="w-3 h-3" />
                  ~{outline.introduction.wordCount} words
                </div>
              </div>

              {/* Chapters */}
              <div className="space-y-4">
                {outline.chapters.map((chapter, chapterIndex) => (
                  <div key={chapterIndex} className="border-l-4 border-purple-500 pl-4">
                    <h3 className="font-semibold text-purple-700 dark:text-purple-300 mb-2">
                      Chapter {chapterIndex + 1}: {chapter.title}
                    </h3>
                    
                    {/* Chapter sections */}
                    <div className="space-y-2 mb-3">
                      {chapter.sections.map((section, sectionIndex) => (
                        <div key={sectionIndex} className="ml-4">
                          <h4 className="font-medium text-sm mb-1">{section.title}</h4>
                          <p className="text-xs text-gray-600 dark:text-gray-400">
                            {section.content}
                          </p>
                        </div>
                      ))}
                    </div>

                    {/* Chapter features */}
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <div className="flex items-center gap-1">
                        <ClockIcon className="w-3 h-3" />
                        ~{chapter.estimatedWordCount} words
                      </div>
                      {chapter.includeTable && (
                        <Badge variant="outline" className="text-xs">Table</Badge>
                      )}
                      {chapter.includeQuote && (
                        <Badge variant="outline" className="text-xs">Quote</Badge>
                      )}
                      {chapter.includeCodeBlock && (
                        <Badge variant="outline" className="text-xs">Code</Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Conclusion */}
              <div className="border-l-4 border-green-500 pl-4">
                <h3 className="font-semibold text-green-700 dark:text-green-300 mb-2">
                  {outline.conclusion.title}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  {outline.conclusion.content}
                </p>
                <div className="flex items-center gap-2 text-xs text-gray-500">
                  <ClockIcon className="w-3 h-3" />
                  ~{outline.conclusion.wordCount} words
                </div>
              </div>

              {/* Metadata */}
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                <h3 className="font-medium mb-3">SEO Metadata</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Categories:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {outline.metadata.categories.map((cat, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {cat}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <span className="font-medium">Tags:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {outline.metadata.tags.map((tag, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Internal Links */}
              {outline.internalLinks.length > 0 && (
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                  <h3 className="font-medium mb-3">Suggested Internal Links</h3>
                  <div className="space-y-2">
                    {outline.internalLinks.slice(0, 5).map((link, index) => (
                      <div key={index} className="text-sm">
                        <span className="font-medium">{link.text}</span>
                        <span className="text-gray-600 dark:text-gray-400 ml-2">
                          → {link.url}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4 border-t">
                <Button onClick={proceedToGeneration} className="flex-1">
                  Proceed to Content Generation →
                </Button>
                <Button variant="outline" onClick={generateOutline}>
                  <PencilIcon className="w-4 h-4 mr-2" />
                  Regenerate
                </Button>
              </div>
            </div>
          </DashboardCardContent>
        </DashboardCard>
      )}

      {/* Generate Outline Button */}
      {!outline && (
        <div className="flex justify-center">
          <Button 
            onClick={generateOutline}
            disabled={generating || !settings || !researchResult}
            size="lg"
            className="px-8"
          >
            {generating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Generating Outline...
              </>
            ) : (
              <>
                <DocumentTextIcon className="w-5 h-5 mr-2" />
                Generate Outline
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  )
}
