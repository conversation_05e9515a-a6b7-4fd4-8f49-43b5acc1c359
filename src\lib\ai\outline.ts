// Outline Generation System
'use client'

import { 
  BlogOutline, 
  OutlineChapter, 
  OutlineSection, 
  ResearchResult, 
  AIRequest,
  InternalLink 
} from '@/types/ai'
import { optimizedAIManager } from './optimization'
import { getOrCreateAISettings } from './settings'
import { sitemapAnalyzer } from './sitemap'

// Outline Generator Class
export class OutlineGenerator {
  private static instance: OutlineGenerator

  static getInstance(): OutlineGenerator {
    if (!OutlineGenerator.instance) {
      OutlineGenerator.instance = new OutlineGenerator()
    }
    return OutlineGenerator.instance
  }

  // Generate comprehensive blog outline
  async generateOutline(
    keywords: string[],
    researchResult: ResearchResult,
    userId: string,
    options: {
      targetWordCount?: number
      targetAudience?: string
      contentType?: 'tutorial' | 'guide' | 'analysis' | 'opinion' | 'news'
      includeIntroConclusion?: boolean
      maxChapters?: number
    } = {}
  ): Promise<BlogOutline> {
    const settings = await getOrCreateAISettings(userId)
    
    // Set defaults
    const {
      targetWordCount = 2000,
      targetAudience = 'intermediate developers',
      contentType = 'guide',
      includeIntroConclusion = true,
      maxChapters = 6
    } = options

    try {
      // Generate the main outline structure
      const outlineStructure = await this.generateOutlineStructure(
        keywords,
        researchResult,
        userId,
        { targetWordCount, targetAudience, contentType, maxChapters }
      )

      // Generate metadata
      const metadata = await this.generateMetadata(
        keywords,
        outlineStructure,
        userId,
        targetAudience
      )

      // Find internal linking opportunities
      const internalLinks = await this.findInternalLinkingOpportunities(
        keywords,
        outlineStructure
      )

      // Create the complete outline
      const outline: BlogOutline = {
        title: outlineStructure.title,
        introduction: outlineStructure.introduction,
        chapters: outlineStructure.chapters,
        conclusion: outlineStructure.conclusion,
        metadata,
        internalLinks,
        externalSources: researchResult.sources
      }

      return outline
    } catch (error) {
      console.error('Failed to generate outline:', error)
      throw new Error(`Outline generation failed: ${error}`)
    }
  }

  private async generateOutlineStructure(
    keywords: string[],
    researchResult: ResearchResult,
    userId: string,
    options: any
  ): Promise<{
    title: string
    introduction: OutlineSection
    chapters: OutlineChapter[]
    conclusion: OutlineSection
  }> {
    const settings = await getOrCreateAISettings(userId)
    
    const outlinePrompt = `
Create a comprehensive blog outline for "${keywords.join(', ')}" based on the following research:

Research Summary: ${researchResult.summary}
Key Points: ${researchResult.keyPoints.join(', ')}
Related Topics: ${researchResult.relatedTopics.join(', ')}

Requirements:
- Target word count: ${options.targetWordCount}
- Target audience: ${options.targetAudience}
- Content type: ${options.contentType}
- Maximum chapters: ${options.maxChapters}

Please provide a detailed outline in the following JSON format:
{
  "title": "Engaging blog post title",
  "introduction": {
    "title": "Introduction",
    "content": "Brief description of introduction content",
    "keyPoints": ["point1", "point2", "point3"],
    "wordCount": 200
  },
  "chapters": [
    {
      "title": "Chapter title",
      "sections": [
        {
          "title": "Section title",
          "content": "Section description",
          "keyPoints": ["point1", "point2"],
          "wordCount": 300
        }
      ],
      "includeTable": true,
      "includeQuote": false,
      "includeCodeBlock": false,
      "estimatedWordCount": 500
    }
  ],
  "conclusion": {
    "title": "Conclusion",
    "content": "Brief description of conclusion content",
    "keyPoints": ["point1", "point2"],
    "wordCount": 150
  }
}

Make sure each chapter has 2-4 sections and includes variety in content types (tables, quotes, code blocks where appropriate).
`

    const request: AIRequest = {
      provider: settings.provider,
      model: settings.models.outline,
      prompt: outlinePrompt,
      maxTokens: 3000,
      temperature: 0.7,
      systemPrompt: 'You are an expert content strategist. Create detailed, engaging blog outlines that provide value to readers.'
    }

    const response = await optimizedAIManager.generateOptimizedResponse(request, userId, settings)
    
    try {
      const outline = JSON.parse(response.content)
      return this.validateAndFixOutline(outline, options)
    } catch (error) {
      console.error('Failed to parse outline JSON:', error)
      return this.createFallbackOutline(keywords, options)
    }
  }

  private validateAndFixOutline(outline: any, options: any): any {
    // Ensure required fields exist
    if (!outline.title) {
      outline.title = 'Complete Guide to Your Topic'
    }

    if (!outline.introduction) {
      outline.introduction = {
        title: 'Introduction',
        content: 'Introduction to the topic',
        keyPoints: ['Overview', 'What you\'ll learn'],
        wordCount: 200
      }
    }

    if (!outline.chapters || !Array.isArray(outline.chapters)) {
      outline.chapters = []
    }

    if (!outline.conclusion) {
      outline.conclusion = {
        title: 'Conclusion',
        content: 'Summary and next steps',
        keyPoints: ['Key takeaways', 'Next steps'],
        wordCount: 150
      }
    }

    // Validate chapters
    outline.chapters = outline.chapters.slice(0, options.maxChapters).map((chapter: any) => {
      if (!chapter.sections || !Array.isArray(chapter.sections)) {
        chapter.sections = [{
          title: chapter.title || 'Section',
          content: 'Section content',
          keyPoints: ['Key point'],
          wordCount: 300
        }]
      }

      // Ensure boolean flags exist
      chapter.includeTable = chapter.includeTable || false
      chapter.includeQuote = chapter.includeQuote || false
      chapter.includeCodeBlock = chapter.includeCodeBlock || false
      
      // Calculate estimated word count
      chapter.estimatedWordCount = chapter.sections.reduce(
        (total: number, section: any) => total + (section.wordCount || 300), 
        0
      )

      return chapter
    })

    return outline
  }

  private createFallbackOutline(keywords: string[], options: any): any {
    const mainKeyword = keywords[0] || 'Topic'
    
    return {
      title: `Complete Guide to ${mainKeyword}`,
      introduction: {
        title: 'Introduction',
        content: `Introduction to ${mainKeyword} and what readers will learn`,
        keyPoints: [`What is ${mainKeyword}`, 'Why it matters', 'What you\'ll learn'],
        wordCount: 200
      },
      chapters: [
        {
          title: `Understanding ${mainKeyword}`,
          sections: [
            {
              title: 'Fundamentals',
              content: `Basic concepts of ${mainKeyword}`,
              keyPoints: ['Core concepts', 'Key terminology'],
              wordCount: 400
            }
          ],
          includeTable: true,
          includeQuote: false,
          includeCodeBlock: false,
          estimatedWordCount: 400
        },
        {
          title: `Best Practices for ${mainKeyword}`,
          sections: [
            {
              title: 'Implementation Tips',
              content: `How to implement ${mainKeyword} effectively`,
              keyPoints: ['Step-by-step approach', 'Common pitfalls'],
              wordCount: 500
            }
          ],
          includeTable: false,
          includeQuote: true,
          includeCodeBlock: true,
          estimatedWordCount: 500
        }
      ],
      conclusion: {
        title: 'Conclusion',
        content: `Summary of ${mainKeyword} guide and next steps`,
        keyPoints: ['Key takeaways', 'Next steps', 'Additional resources'],
        wordCount: 150
      }
    }
  }

  private async generateMetadata(
    keywords: string[],
    outline: any,
    userId: string,
    targetAudience: string
  ): Promise<BlogOutline['metadata']> {
    const settings = await getOrCreateAISettings(userId)
    
    const metadataPrompt = `
Based on this blog outline, generate SEO-optimized metadata:

Title: ${outline.title}
Chapters: ${outline.chapters.map((c: any) => c.title).join(', ')}
Keywords: ${keywords.join(', ')}
Target Audience: ${targetAudience}

Provide metadata in JSON format:
{
  "estimatedWordCount": 2000,
  "targetAudience": "${targetAudience}",
  "seoKeywords": ["keyword1", "keyword2", "keyword3"],
  "categories": ["category1", "category2"],
  "tags": ["tag1", "tag2", "tag3", "tag4", "tag5"]
}

Focus on relevant, searchable keywords and appropriate categorization.
`

    const request: AIRequest = {
      provider: settings.provider,
      model: settings.models.metadata,
      prompt: metadataPrompt,
      maxTokens: 1000,
      temperature: 0.3,
      systemPrompt: 'You are an SEO expert. Generate relevant, searchable metadata for blog posts.'
    }

    try {
      const response = await optimizedAIManager.generateOptimizedResponse(request, userId, settings)
      const metadata = JSON.parse(response.content)
      
      return {
        estimatedWordCount: metadata.estimatedWordCount || 2000,
        targetAudience: metadata.targetAudience || targetAudience,
        seoKeywords: metadata.seoKeywords || keywords,
        categories: metadata.categories || ['General'],
        tags: metadata.tags || keywords
      }
    } catch (error) {
      console.error('Failed to generate metadata:', error)
      return {
        estimatedWordCount: 2000,
        targetAudience,
        seoKeywords: keywords,
        categories: ['Technology'],
        tags: keywords
      }
    }
  }

  private async findInternalLinkingOpportunities(
    keywords: string[],
    outline: any
  ): Promise<InternalLink[]> {
    try {
      // Create content string from outline for analysis
      const outlineContent = [
        outline.title,
        outline.introduction.content,
        ...outline.chapters.flatMap((chapter: any) => [
          chapter.title,
          ...chapter.sections.map((section: any) => section.content)
        ]),
        outline.conclusion.content
      ].join(' ')

      const opportunities = await sitemapAnalyzer.findLinkingOpportunities(
        outlineContent,
        'new-post', // Temporary slug for new post
        keywords
      )

      return opportunities.slice(0, 8).map(opp => ({
        text: opp.anchorText,
        url: opp.targetUrl,
        relevanceScore: opp.relevanceScore,
        context: opp.context
      }))
    } catch (error) {
      console.error('Failed to find internal linking opportunities:', error)
      return []
    }
  }

  // Refine existing outline
  async refineOutline(
    outline: BlogOutline,
    feedback: string,
    userId: string
  ): Promise<BlogOutline> {
    const settings = await getOrCreateAISettings(userId)
    
    const refinementPrompt = `
Please refine this blog outline based on the feedback provided:

Current Outline:
Title: ${outline.title}
Chapters: ${outline.chapters.map(c => c.title).join(', ')}

Feedback: ${feedback}

Provide the refined outline in the same JSON format as the original, incorporating the feedback while maintaining structure and quality.
`

    const request: AIRequest = {
      provider: settings.provider,
      model: settings.models.outline,
      prompt: refinementPrompt,
      maxTokens: 2500,
      temperature: 0.6,
      systemPrompt: 'You are a content editor. Refine outlines based on feedback while maintaining quality and structure.'
    }

    try {
      const response = await optimizedAIManager.generateOptimizedResponse(request, userId, settings)
      const refinedOutline = JSON.parse(response.content)
      
      return {
        ...outline,
        ...refinedOutline,
        metadata: outline.metadata, // Preserve original metadata
        internalLinks: outline.internalLinks, // Preserve internal links
        externalSources: outline.externalSources // Preserve sources
      }
    } catch (error) {
      console.error('Failed to refine outline:', error)
      return outline // Return original if refinement fails
    }
  }

  // Estimate reading time for outline
  estimateReadingTime(outline: BlogOutline): number {
    const totalWords = outline.metadata.estimatedWordCount
    const wordsPerMinute = 200 // Average reading speed
    return Math.ceil(totalWords / wordsPerMinute)
  }

  // Validate outline completeness
  validateOutline(outline: BlogOutline): { isValid: boolean; issues: string[] } {
    const issues: string[] = []

    if (!outline.title || outline.title.length < 10) {
      issues.push('Title is too short or missing')
    }

    if (!outline.chapters || outline.chapters.length < 2) {
      issues.push('Outline needs at least 2 chapters')
    }

    if (outline.metadata.estimatedWordCount < 500) {
      issues.push('Content appears too short for a comprehensive blog post')
    }

    outline.chapters.forEach((chapter, index) => {
      if (!chapter.sections || chapter.sections.length === 0) {
        issues.push(`Chapter ${index + 1} has no sections`)
      }
    })

    return {
      isValid: issues.length === 0,
      issues
    }
  }
}

// Export singleton instance
export const outlineGenerator = OutlineGenerator.getInstance()

// Utility functions
export const generateBlogOutline = async (
  keywords: string[],
  researchResult: ResearchResult,
  userId: string,
  options?: any
): Promise<BlogOutline> => {
  return await outlineGenerator.generateOutline(keywords, researchResult, userId, options)
}

export const validateBlogOutline = (outline: BlogOutline) => {
  return outlineGenerator.validateOutline(outline)
}
