// Content Generation Pipeline
'use client'

import { 
  BlogOutline, 
  OutlineChapter, 
  OutlineSection, 
  ResearchResult, 
  AIRequest,
  AISettings,
  GenerationProgress 
} from '@/types/ai'
import { optimizedAIManager } from './optimization'
import { getOrCreateAISettings } from './settings'
import { insertAutoLinks } from './sitemap'
import { markdownFormatter, enhanceContentWithMarkdown } from './markdown-formatter'
import { addSmartInternalLinks } from './internal-linking'
import { addExternalCitations } from './citation-system'
import { generateContentMetadata } from './metadata-generator'
import { validateContentQuality } from './quality-validator'

export interface GeneratedContent {
  title: string
  content: string
  excerpt: string
  metadata: {
    categories: string[]
    tags: string[]
    estimatedReadingTime: number
    wordCount: number
  }
  tokensUsed: number
  cost: number
}

export interface ContentGenerationOptions {
  includeIntroduction: boolean
  includeConclusion: boolean
  enableInternalLinking: boolean
  enableExternalCitations: boolean
  maxLinksPerSection: number
  writingStyle: 'professional' | 'casual' | 'technical' | 'conversational'
  targetAudience: string
}

// Content Generator Class
export class ContentGenerator {
  private static instance: ContentGenerator

  static getInstance(): ContentGenerator {
    if (!ContentGenerator.instance) {
      ContentGenerator.instance = new ContentGenerator()
    }
    return ContentGenerator.instance
  }

  // Main content generation method
  async generateContent(
    outline: BlogOutline,
    researchResult: ResearchResult,
    userId: string,
    options: ContentGenerationOptions = {
      includeIntroduction: true,
      includeConclusion: true,
      enableInternalLinking: true,
      enableExternalCitations: true,
      maxLinksPerSection: 3,
      writingStyle: 'professional',
      targetAudience: 'intermediate developers'
    },
    onProgress?: (progress: GenerationProgress) => void
  ): Promise<GeneratedContent> {
    const settings = await getOrCreateAISettings(userId)
    let totalTokensUsed = 0
    let totalCost = 0
    let fullContent = ''

    try {
      // Generate title and introduction
      onProgress?.({
        stage: 'content',
        progress: 5,
        message: 'Generating introduction...',
        tokensUsed: totalTokensUsed,
        estimatedCost: totalCost
      })

      fullContent += `# ${outline.title}\n\n`

      if (options.includeIntroduction) {
        const introContent = await this.generateSection(
          outline.introduction,
          researchResult,
          settings,
          userId,
          options,
          'introduction'
        )
        fullContent += introContent.content + '\n\n'
        totalTokensUsed += introContent.tokensUsed
        totalCost += introContent.cost
      }

      // Generate chapters in batches of 2
      const totalChapters = outline.chapters.length
      for (let i = 0; i < totalChapters; i += 2) {
        const batch = outline.chapters.slice(i, i + 2)
        const batchProgress = 10 + ((i / totalChapters) * 70)

        onProgress?.({
          stage: 'content',
          currentChapter: i + 1,
          totalChapters,
          progress: batchProgress,
          message: `Generating chapters ${i + 1}-${Math.min(i + 2, totalChapters)}...`,
          tokensUsed: totalTokensUsed,
          estimatedCost: totalCost
        })

        // Generate batch content
        const batchContent = await this.generateChapterBatch(
          batch,
          researchResult,
          settings,
          userId,
          options,
          i
        )

        fullContent += batchContent.content + '\n\n'
        totalTokensUsed += batchContent.tokensUsed
        totalCost += batchContent.cost

        // Update progress
        onProgress?.({
          stage: 'content',
          currentChapter: Math.min(i + 2, totalChapters),
          totalChapters,
          progress: 10 + (((i + 2) / totalChapters) * 70),
          message: `Completed chapters ${i + 1}-${Math.min(i + 2, totalChapters)}`,
          tokensUsed: totalTokensUsed,
          estimatedCost: totalCost
        })

        // Add delay between batches to avoid rate limits
        if (i + 2 < totalChapters) {
          await new Promise(resolve => setTimeout(resolve, 2000))
        }
      }

      // Generate conclusion
      if (options.includeConclusion) {
        onProgress?.({
          stage: 'content',
          progress: 85,
          message: 'Generating conclusion...',
          tokensUsed: totalTokensUsed,
          estimatedCost: totalCost
        })

        const conclusionContent = await this.generateSection(
          outline.conclusion,
          researchResult,
          settings,
          userId,
          options,
          'conclusion'
        )
        fullContent += conclusionContent.content
        totalTokensUsed += conclusionContent.tokensUsed
        totalCost += conclusionContent.cost
      }

      // Apply internal linking if enabled
      if (options.enableInternalLinking) {
        onProgress?.({
          stage: 'content',
          progress: 90,
          message: 'Adding internal links...',
          tokensUsed: totalTokensUsed,
          estimatedCost: totalCost
        })

        fullContent = await insertAutoLinks(
          fullContent,
          'new-post', // Temporary slug for new post
          outline.metadata.seoKeywords,
          options.maxLinksPerSection * outline.chapters.length
        )
      }

      // Generate excerpt
      const excerpt = await this.generateExcerpt(fullContent, settings, userId)
      totalTokensUsed += excerpt.tokensUsed
      totalCost += excerpt.cost

      onProgress?.({
        stage: 'complete',
        progress: 100,
        message: 'Content generation complete!',
        tokensUsed: totalTokensUsed,
        estimatedCost: totalCost
      })

      return {
        title: outline.title,
        content: fullContent,
        excerpt: excerpt.content,
        metadata: {
          categories: outline.metadata.categories,
          tags: outline.metadata.tags,
          estimatedReadingTime: this.calculateReadingTime(fullContent),
          wordCount: this.countWords(fullContent)
        },
        tokensUsed: totalTokensUsed,
        cost: totalCost
      }

    } catch (error) {
      console.error('Content generation failed:', error)
      throw new Error(`Content generation failed: ${error}`)
    }
  }

  private async generateChapterBatch(
    chapters: OutlineChapter[],
    researchResult: ResearchResult,
    settings: AISettings,
    userId: string,
    options: ContentGenerationOptions,
    batchIndex: number
  ): Promise<{ content: string; tokensUsed: number; cost: number }> {
    let batchContent = ''
    let totalTokens = 0
    let totalCost = 0

    for (const chapter of chapters) {
      const chapterContent = await this.generateChapter(
        chapter,
        researchResult,
        settings,
        userId,
        options
      )
      
      batchContent += chapterContent.content + '\n\n'
      totalTokens += chapterContent.tokensUsed
      totalCost += chapterContent.cost
    }

    return {
      content: batchContent,
      tokensUsed: totalTokens,
      cost: totalCost
    }
  }

  private async generateChapter(
    chapter: OutlineChapter,
    researchResult: ResearchResult,
    settings: AISettings,
    userId: string,
    options: ContentGenerationOptions
  ): Promise<{ content: string; tokensUsed: number; cost: number }> {
    // Prepare research context
    const relevantSources = researchResult.sources
      .slice(0, 3)
      .map(source => `Source: ${source.title}\nContent: ${source.content.substring(0, 300)}...`)
      .join('\n\n')

    const chapterPrompt = `
Write a comprehensive chapter for a blog post with the following specifications:

Chapter Title: ${chapter.title}
Target Word Count: ${chapter.estimatedWordCount}
Writing Style: ${options.writingStyle}
Target Audience: ${options.targetAudience}

Chapter Sections:
${chapter.sections.map(section => `- ${section.title}: ${section.content}`).join('\n')}

Special Requirements:
${chapter.includeTable ? '- Include a relevant table with data' : ''}
${chapter.includeQuote ? '- Include an expert quote or testimonial' : ''}
${chapter.includeCodeBlock ? '- Include a code example with explanation' : ''}

Research Context:
${relevantSources}

Key Points to Cover:
${researchResult.keyPoints.slice(0, 5).join('\n')}

Instructions:
1. Write in ${options.writingStyle} style for ${options.targetAudience}
2. Use proper Markdown formatting with headers (##, ###)
3. Include the specified special content types if required
4. Make content engaging and informative
5. Use examples and practical applications
6. Ensure smooth flow between sections
${options.enableExternalCitations ? '7. Include relevant citations where appropriate' : ''}

Format the response as clean Markdown without any wrapper text.
`

    const request: AIRequest = {
      provider: settings.provider,
      model: settings.models.content,
      prompt: chapterPrompt,
      maxTokens: Math.min(chapter.estimatedWordCount * 2, settings.preferences.maxTokensPerRequest),
      temperature: settings.preferences.temperature,
      systemPrompt: 'You are an expert content writer. Create high-quality, engaging blog content that provides real value to readers.'
    }

    try {
      const response = await optimizedAIManager.generateOptimizedResponse(request, userId, settings)
      
      let content = `## ${chapter.title}\n\n${response.content}`
      
      // Ensure proper formatting
      content = this.formatContent(content, chapter)
      
      return {
        content,
        tokensUsed: response.tokensUsed,
        cost: response.cost
      }
    } catch (error) {
      console.error(`Failed to generate chapter: ${chapter.title}`, error)
      // Fallback content
      return {
        content: `## ${chapter.title}\n\nContent generation failed for this chapter. Please regenerate or edit manually.`,
        tokensUsed: 0,
        cost: 0
      }
    }
  }

  private async generateSection(
    section: OutlineSection,
    researchResult: ResearchResult,
    settings: AISettings,
    userId: string,
    options: ContentGenerationOptions,
    sectionType: 'introduction' | 'conclusion'
  ): Promise<{ content: string; tokensUsed: number; cost: number }> {
    const sectionPrompt = `
Write a compelling ${sectionType} for a blog post:

Title: ${section.title}
Target Word Count: ${section.wordCount}
Writing Style: ${options.writingStyle}
Target Audience: ${options.targetAudience}

Content Description: ${section.content}

Key Points to Address:
${section.keyPoints.join('\n')}

${sectionType === 'introduction' ? `
Research Summary: ${researchResult.summary}

Instructions for Introduction:
1. Hook the reader with an engaging opening
2. Clearly state what the post will cover
3. Explain why this topic matters to the reader
4. Set expectations for what they'll learn
` : `
Instructions for Conclusion:
1. Summarize the key takeaways
2. Provide actionable next steps
3. Encourage engagement or further learning
4. End with a compelling call-to-action
`}

Format as clean Markdown with proper heading (## for ${section.title}).
`

    const request: AIRequest = {
      provider: settings.provider,
      model: settings.models.content,
      prompt: sectionPrompt,
      maxTokens: section.wordCount * 2,
      temperature: settings.preferences.temperature,
      systemPrompt: 'You are an expert content writer specializing in engaging introductions and conclusions.'
    }

    try {
      const response = await optimizedAIManager.generateOptimizedResponse(request, userId, settings)
      
      const content = `## ${section.title}\n\n${response.content}`
      
      return {
        content,
        tokensUsed: response.tokensUsed,
        cost: response.cost
      }
    } catch (error) {
      console.error(`Failed to generate ${sectionType}`, error)
      return {
        content: `## ${section.title}\n\nContent generation failed. Please edit manually.`,
        tokensUsed: 0,
        cost: 0
      }
    }
  }

  private async generateExcerpt(
    content: string,
    settings: AISettings,
    userId: string
  ): Promise<{ content: string; tokensUsed: number; cost: number }> {
    const excerptPrompt = `
Create a compelling excerpt (150-200 words) for this blog post that will entice readers to read the full article:

Content: ${content.substring(0, 1000)}...

Instructions:
1. Capture the main value proposition
2. Make it engaging and clickable
3. Include key benefits for the reader
4. Keep it between 150-200 words
5. Don't include markdown formatting
`

    const request: AIRequest = {
      provider: settings.provider,
      model: settings.models.metadata,
      prompt: excerptPrompt,
      maxTokens: 300,
      temperature: 0.7,
      systemPrompt: 'You are an expert at writing compelling blog post excerpts that drive engagement.'
    }

    try {
      const response = await optimizedAIManager.generateOptimizedResponse(request, userId, settings)
      return {
        content: response.content.trim(),
        tokensUsed: response.tokensUsed,
        cost: response.cost
      }
    } catch (error) {
      console.error('Failed to generate excerpt', error)
      return {
        content: 'An engaging blog post about the topic.',
        tokensUsed: 0,
        cost: 0
      }
    }
  }

  private formatContent(content: string, chapter: OutlineChapter): string {
    // Ensure proper spacing and formatting
    let formatted = content
      .replace(/\n{3,}/g, '\n\n') // Remove excessive line breaks
      .replace(/^#+\s*/gm, (match) => match) // Ensure headers have proper spacing
      .trim()

    return formatted
  }

  private calculateReadingTime(content: string): number {
    const wordsPerMinute = 200
    const wordCount = this.countWords(content)
    return Math.ceil(wordCount / wordsPerMinute)
  }

  private countWords(content: string): number {
    // Remove markdown formatting for accurate word count
    const plainText = content
      .replace(/#{1,6}\s+/g, '') // Remove headers
      .replace(/\*\*([^*]+)\*\*/g, '$1') // Remove bold
      .replace(/\*([^*]+)\*/g, '$1') // Remove italic
      .replace(/`([^`]+)`/g, '$1') // Remove inline code
      .replace(/```[\s\S]*?```/g, '') // Remove code blocks
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove links
      .replace(/!\[([^\]]*)\]\([^)]+\)/g, '') // Remove images
      .replace(/\|[^|\n]*\|/g, '') // Remove tables
      .replace(/>\s+/g, '') // Remove quotes
      .trim()

    return plainText.split(/\s+/).filter(word => word.length > 0).length
  }
}

// Export singleton instance
export const contentGenerator = ContentGenerator.getInstance()

// Utility functions
export const generateBlogContent = async (
  outline: BlogOutline,
  researchResult: ResearchResult,
  userId: string,
  options?: ContentGenerationOptions,
  onProgress?: (progress: GenerationProgress) => void
): Promise<GeneratedContent> => {
  return await contentGenerator.generateContent(outline, researchResult, userId, options, onProgress)
}

export const estimateGenerationCost = (outline: BlogOutline, settings: AISettings): number => {
  const estimatedTokens = outline.metadata.estimatedWordCount * 1.5 // Rough estimation
  return optimizedAIManager.estimateCost({
    provider: settings.provider,
    model: settings.models.content,
    prompt: 'sample prompt',
    maxTokens: estimatedTokens
  })
}
