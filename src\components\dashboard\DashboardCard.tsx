import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { cn } from '@/lib/utils'

interface DashboardCardProps {
  children: React.ReactNode
  className?: string
}

interface DashboardCardHeaderProps {
  children: React.ReactNode
  className?: string
}

interface DashboardCardContentProps {
  children: React.ReactNode
  className?: string
}

interface DashboardCardTitleProps {
  children: React.ReactNode
  className?: string
}

interface DashboardCardDescriptionProps {
  children: React.ReactNode
  className?: string
}

export function DashboardCard({ children, className }: DashboardCardProps) {
  return (
    <Card className={cn('dashboard-card border', className)}>
      {children}
    </Card>
  )
}

export function DashboardCardHeader({ children, className }: DashboardCardHeaderProps) {
  return (
    <CardHeader className={className}>
      {children}
    </CardHeader>
  )
}

export function DashboardCardContent({ children, className }: DashboardCardContentProps) {
  return (
    <CardContent className={className}>
      {children}
    </CardContent>
  )
}

export function DashboardCardTitle({ children, className }: DashboardCardTitleProps) {
  return (
    <CardTitle className={cn('dashboard-text', className)}>
      {children}
    </CardTitle>
  )
}

export function DashboardCardDescription({ children, className }: DashboardCardDescriptionProps) {
  return (
    <CardDescription className={cn('dashboard-muted', className)}>
      {children}
    </CardDescription>
  )
}
