'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import Image from 'next/image'
import { Project } from '@/types'

interface ProjectGridProps {
  projects: Project[]
}

export default function ProjectGrid({ projects }: ProjectGridProps) {
  const [hoveredCard, setHoveredCard] = useState<number | null>(null)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })

  const handleMouseMove = (e: React.MouseEvent, cardIndex: number) => {
    const rect = e.currentTarget.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top
    const centerX = rect.width / 2
    const centerY = rect.height / 2

    // Calculate rotation values (-1 to 1 range)
    const rotateX = (y - centerY) / centerY
    const rotateY = (x - centerX) / centerX

    setMousePosition({
      x: rotateY,
      y: rotateX
    })
  }

  const handleMouseEnter = (cardIndex: number) => {
    setHoveredCard(cardIndex)
  }

  const handleMouseLeave = () => {
    setHoveredCard(null)
    setMousePosition({ x: 0, y: 0 })
  }

  if (projects.length === 0) {
    return (
      <div className="text-center py-16">
        <h2 className="text-2xl font-semibold text-gray-600 mb-4">No projects yet</h2>
        <p className="text-gray-500">Check back soon for new projects!</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-20">
      {projects.map((project, index) => (
        <motion.article
          key={project.slug}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: index * 0.1 }}
          className="group h-full"
          onMouseMove={(e) => handleMouseMove(e, index)}
          onMouseEnter={() => handleMouseEnter(index)}
          onMouseLeave={handleMouseLeave}
        >
          <div className="h-full flex flex-col">
            {/* Featured Image - Bigger with subtle tilt effect */}
            <div className="relative h-80 mb-6" style={{ perspective: "1000px" }}>
              <motion.div
                className="relative h-full w-full"
                animate={{
                  rotateX: hoveredCard === index ? mousePosition.y * 3 : 0,
                  rotateY: hoveredCard === index ? mousePosition.x * 3 : 0,
                }}
                transition={{
                  type: "spring",
                  stiffness: 300,
                  damping: 20
                }}
                style={{
                  transformStyle: "preserve-3d",
                  transformOrigin: "center center"
                }}
              >
                <Link href={`/projects/${project.slug}`} className="block w-full h-full">
                  <div className="w-full h-full rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
                    {project.featuredImage && project.featuredImage.startsWith('http') ? (
                      <Image
                        src={project.featuredImage}
                        alt={project.title}
                        width={600}
                        height={320}
                        className="object-cover object-center w-full h-full"
                        priority={index < 2}
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                        <span className="text-white text-2xl font-bold">{project.title.charAt(0)}</span>
                      </div>
                    )}
                  </div>
                </Link>
              </motion.div>
            </div>

            {/* Content - No card wrapper */}
            <div className="flex-1 flex flex-col">
              {/* Title */}
              <Link href={`/projects/${project.slug}`} className="no-link-style">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300">
                  {project.title}
                </h2>
              </Link>

              {/* Client & Industry in same row */}
              <div className="flex items-center justify-between">
                {project.client ? (
                  <a
                    href={project.liveUrl || '#'}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 dark:text-blue-400 font-medium hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-300 cursor-pointer"
                  >
                    {project.client}
                  </a>
                ) : (
                  <span className="text-gray-500 dark:text-gray-400">No client specified</span>
                )}

                {project.industry && (
                  <span className="text-gray-600 dark:text-gray-400 text-sm">
                    {project.industry}
                  </span>
                )}
              </div>
            </div>
          </div>
        </motion.article>
      ))}
    </div>
  )
}
